/**
 * Authentication State using Svelte 5 Runes and Context Pattern
 *
 * This module provides reactive authentication state management using the
 * reactive class pattern with Svelte 5 runes. It handles user authentication
 * status, login/logout operations, and provides context for the entire app.
 *
 * Key features:
 * - Cookie-based authentication (no localStorage)
 * - Server-side state initialization
 * - Reactive class pattern with $state runes
 * - Context pattern for sharing state across components
 */

import { getContext, setContext } from "svelte";
import { browser } from "$app/environment";
import { APP_CONFIG } from "$lib/config/env";
import type { User, LoginCredentials } from "$types/auth";
import { goto } from "$app/navigation";

/**
 * Authentication state interface
 */
interface AuthState {
	isAuthenticated: boolean;
	isLoading: boolean;
	user: User | null;
	error: string | null;
	login: (credentials: LoginCredentials) => Promise<boolean>;
	logout: () => Promise<void>;
	clearError: () => void;
}

/**
 * Reactive authentication state class using Svelte 5 runes
 */
class AuthStateClass implements AuthState {
	isAuthenticated = $state(false);
	isLoading = $state(false);
	user = $state<User | null>(null);
	error = $state<string | null>(null);

	/**
	 * Initialize auth state with server-provided data
	 */
	constructor(initialUser: User | null = null) {
		if (initialUser) {
			this.user = initialUser;
			this.isAuthenticated = true;
		}
	}

	/**
	 * Login user with email and password
	 * Uses server-side form submission for cookie-based auth
	 */
	login = async (credentials: LoginCredentials): Promise<boolean> => {
		this.isLoading = true;
		this.error = null;

		try {
			// Use fetch to submit to the server action
			const formData = new FormData();
			formData.append("email", credentials.email);
			formData.append("password", credentials.password);

			const response = await fetch("/login", {
				method: "POST",
				body: formData,
				credentials: "include", // Important for cookies
			});

			if (response.ok) {
				// Check if we got redirected (successful login)
				if (response.redirected) {
					// Login successful, navigate to the redirect URL
					if (browser) {
						window.location.href = response.url;
					}
					return true;
				}

				// If not redirected, parse the response for errors
				const result = await response.text();
				// This would contain the form with errors
				this.error = "Login failed. Please check your credentials.";
				return false;
			} else {
				this.error = "Login failed. Please try again.";
				return false;
			}
		} catch (error) {
			console.error("Login error:", error);
			this.error = "An unexpected error occurred. Please try again.";
			return false;
		} finally {
			this.isLoading = false;
		}
	};

	/**
	 * Logout user and clear authentication state
	 * Clears both client state and server-side cookies
	 */
	logout = async (): Promise<void> => {
		this.isLoading = true;

		try {
			// Clear server-side cookies by calling logout endpoint
			await fetch("/api/auth/logout", {
				method: "POST",
				credentials: "include",
			});
		} catch (error) {
			console.error("Logout error:", error);
			// Continue with client-side cleanup even if server request fails
		}

		// Clear client-side state
		this.user = null;
		this.isAuthenticated = false;
		this.error = null;
		this.isLoading = false;

		// Redirect to login page
		if (browser) {
			goto(APP_CONFIG.ROUTES.LOGIN);
		}
	};

	/**
	 * Clear any authentication errors
	 */
	clearError = (): void => {
		this.error = null;
	};
}

/**
 * Context key for authentication state
 */
const AUTH_CONTEXT_KEY = "auth_state";

/**
 * Set authentication context in a parent component (usually root layout)
 */
export const setAuthState = (initialUser: User | null = null): AuthState => {
	const authState = new AuthStateClass(initialUser);
	return setContext(AUTH_CONTEXT_KEY, authState);
};

/**
 * Get authentication context from a child component
 */
export const getAuthState = (): AuthState => {
	const authState = getContext<AuthState>(AUTH_CONTEXT_KEY);
	if (!authState) {
		throw new Error(
			"Auth context not found. Make sure to call setAuthState in a parent component.",
		);
	}
	return authState;
};

/**
 * Utility functions for checking user permissions and roles
 */
export const authUtils = {
	/**
	 * Check if user has a specific permission
	 */
	hasPermission: (user: User | null, permission: string): boolean => {
		if (!user?.user_role?.permissions) return false;
		return user.user_role.permissions.some(
			(p) => p.permission_name === permission,
		);
	},

	/**
	 * Check if user has a specific role
	 */
	hasRole: (user: User | null, role: string): boolean => {
		return user?.user_role?.role_name === role;
	},
};
