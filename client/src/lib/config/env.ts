/**
 * Environment Configuration
 * 
 * This file centralizes environment-specific configuration values
 * and provides defaults for development. In production, these values
 * should be overridden by environment variables.
 */

import { browser } from '$app/environment';
import { env } from '$env/dynamic/public';

/**
 * API Configuration
 */
export const API_CONFIG = {
	// Base URL for the Django backend API
	BASE_URL: env.PUBLIC_API_BASE_URL || 'http://localhost:8000',
	
	// API endpoints
	ENDPOINTS: {
		LOGIN: '/auth/login/',
		REFRESH: '/auth/token/refresh/',
		USERS: '/auth/users/',
		LOGOUT: '/auth/logout/', // If backend supports logout endpoint
	},
	
	// Request timeout in milliseconds
	TIMEOUT: 10000,
} as const;

/**
 * Authentication Configuration
 */
export const AUTH_CONFIG = {
	// Token storage keys
	STORAGE_KEYS: {
		ACCESS_TOKEN: 'auth_access_token',
		REFRESH_TOKEN: 'auth_refresh_token',
		USER: 'auth_user',
	},
	
	// Token expiration times (in seconds)
	TOKEN_EXPIRY: {
		ACCESS: 15 * 60, // 15 minutes
		REFRESH: 7 * 24 * 60 * 60, // 7 days
	},
	
	// Auto-refresh token when it expires in this many seconds
	REFRESH_THRESHOLD: 5 * 60, // 5 minutes
} as const;

/**
 * Application Configuration
 */
export const APP_CONFIG = {
	// Default redirect paths
	ROUTES: {
		LOGIN: '/login',
		DASHBOARD: '/dashboard',
		HOME: '/',
	},
	
	// Feature flags
	FEATURES: {
		GOOGLE_AUTH: env.PUBLIC_ENABLE_GOOGLE_AUTH === 'true',
		REMEMBER_ME: true,
		AUTO_LOGOUT: true,
	},
} as const;

/**
 * Development Configuration
 */
export const DEV_CONFIG = {
	// Enable debug logging in development
	DEBUG: !browser || env.NODE_ENV === 'development',
	
	// Mock API responses for development
	MOCK_API: env.PUBLIC_MOCK_API === 'true',
	
	// Show detailed error messages
	VERBOSE_ERRORS: env.NODE_ENV === 'development',
} as const;

/**
 * Utility function to check if we're in development mode
 */
export function isDevelopment(): boolean {
	return DEV_CONFIG.DEBUG;
}

/**
 * Utility function to get the full API URL
 */
export function getApiUrl(endpoint: string): string {
	return `${API_CONFIG.BASE_URL}${endpoint}`;
}

/**
 * Utility function to log debug messages (only in development)
 */
export function debugLog(message: string, ...args: any[]): void {
	if (DEV_CONFIG.DEBUG) {
		console.log(`[DEBUG] ${message}`, ...args);
	}
}
