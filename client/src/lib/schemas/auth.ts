/**
 * Authentication form validation schemas using Zod
 *
 * This file contains all Zod schemas for validating authentication-related forms,
 * including login, registration, password reset, and other auth operations.
 * These schemas ensure data integrity and provide client-side validation.
 */

import { z } from "zod";

/**
 * Login form validation schema
 *
 * Validates email and password fields with appropriate constraints:
 * - Email: Must be a valid email format
 * - Password: Minimum 8 characters for security
 */
export const loginSchema = z.object({
	email: z
		.string()
		.min(1, "Email is required")
		.email("Please enter a valid email address")
		.max(254, "Email is too long"),

	password: z
		.string()
		.min(1, "Password is required")
		.min(8, "Password must be at least 8 characters long")
		.max(128, "Password is too long")
});

/**
 * Password reset request schema
 *
 * Validates email field for password reset requests
 */
export const passwordResetSchema = z.object({
	email: z
		.string()
		.min(1, "Email is required")
		.email("Please enter a valid email address")
		.max(254, "Email is too long")
});

/**
 * Password reset confirmation schema
 *
 * Validates new password and confirmation fields
 */
export const passwordResetConfirmSchema = z
	.object({
		password: z
			.string()
			.min(8, "Password must be at least 8 characters long")
			.max(128, "Password is too long")
			.regex(
				/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
				"Password must contain at least one lowercase letter, one uppercase letter, and one number"
			),

		confirmPassword: z.string().min(1, "Please confirm your password")
	})
	.refine((data) => data.password === data.confirmPassword, {
		message: "Passwords don't match",
		path: ["confirmPassword"]
	});

/**
 * User registration schema (for future use)
 *
 * Validates all fields required for user registration
 */
export const registrationSchema = z
	.object({
		username: z
			.string()
			.min(1, "Username is required")
			.min(3, "Username must be at least 3 characters long")
			.max(150, "Username is too long")
			.regex(
				/^[a-zA-Z0-9_-]+$/,
				"Username can only contain letters, numbers, underscores, and hyphens"
			),

		email: z
			.string()
			.min(1, "Email is required")
			.email("Please enter a valid email address")
			.max(254, "Email is too long"),

		password: z
			.string()
			.min(8, "Password must be at least 8 characters long")
			.max(128, "Password is too long")
			.regex(
				/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
				"Password must contain at least one lowercase letter, one uppercase letter, and one number"
			),

		confirmPassword: z.string().min(1, "Please confirm your password")
	})
	.refine((data) => data.password === data.confirmPassword, {
		message: "Passwords don't match",
		path: ["confirmPassword"]
	});

/**
 * Type inference for form data
 * These types are automatically inferred from the Zod schemas above
 */
export type LoginFormData = z.infer<typeof loginSchema>;
export type PasswordResetFormData = z.infer<typeof passwordResetSchema>;
export type PasswordResetConfirmFormData = z.infer<typeof passwordResetConfirmSchema>;
export type RegistrationFormData = z.infer<typeof registrationSchema>;
