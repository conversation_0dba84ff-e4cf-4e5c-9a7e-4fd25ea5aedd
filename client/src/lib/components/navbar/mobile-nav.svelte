<script lang="ts">
	import { NavHamburgerMenuIcon } from "$lib/components/icons/ui";
	import type { HTMLAttributes } from "svelte/elements";

	let {
		isMobileNavOpen = $bindable(false),
		...props
	}: { isMobileNavOpen: boolean } & HTMLAttributes<HTMLButtonElement> = $props();
</script>

<button onclick={() => (isMobileNavOpen = !isMobileNavOpen)} {...props}>
	<NavHamburgerMenuIcon {isMobileNavOpen} class="size-6 cursor-pointer" />
</button>
