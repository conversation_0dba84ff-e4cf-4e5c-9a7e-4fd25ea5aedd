<script lang="ts">
	import { BrandLogoIconWithText } from "../icons/brand";
	import { MobileNav } from "$lib/components/navbar";
	import { navLinks } from "$config";
	import {
		getDefaultNavLinks,
		getLoginLink,
		getScheduleDemoLink,
		getTopLevelLinks,
		isLinkActive
	} from "$lib/custom-utils/index";
	import clsx from "clsx";
	import { Button } from "../ui/button";
	import NavLinkList from "./NavLinkList.svelte";
	import { page } from "$app/stores";
	import LightSwitch from "../LightSwitch.svelte";

	/**
	 * Mobile navigation open/closed state
	 */
	let isMobileNavOpen = $state(false);

	/**
	 * Track which dropdown is currently active/open
	 * Only one dropdown can be open at a time for better UX
	 */
	let activeDropdownId = $state<string | null>(null);

	// ============================================================================
	// Navigation Link Processing
	// ============================================================================

	/**
	 * Mobile navigation links - flattened structure for simple mobile menu
	 */
	const defaultLeafNavLinks = getDefaultNavLinks(navLinks.filter((link) => link.label !== "Legal"));

	/**
	 * Authentication and CTA links
	 */
	const loginLink = getLoginLink(navLinks);
	const scheduleDemoLink = getScheduleDemoLink(navLinks);

	/**
	 * Desktop/tablet navigation links - excludes Legal and auth links
	 * These are handled separately in the header layout
	 */
	const topLevelLinks = getTopLevelLinks(navLinks).filter((link) => link.label !== "Legal");

	/**
	 * Navigation links for desktop display - excludes auth CTAs
	 */
	const desktopNavLinks = topLevelLinks.filter(
		(link) => link.label !== "Login" && link.label !== "Schedule Demo"
	);

	// ============================================================================
	// Event Handlers
	// ============================================================================

	/**
	 * Opens a dropdown menu by setting the active dropdown ID
	 * @param linkId - The label/ID of the link whose dropdown should be opened
	 */
	function handleDropdownEnter(linkId: string) {
		activeDropdownId = linkId;
	}

	/**
	 * Closes any open dropdown menu
	 */
	function handleDropdownLeave() {
		activeDropdownId = null;
	}

	/**
	 * Closes mobile navigation when mouse leaves the header
	 * Provides intuitive UX for hover-based mobile nav
	 */
	function handleHeaderMouseLeave() {
		isMobileNavOpen = false;
	}

	/**
	 * Closes mobile navigation when a link is clicked in mobile view
	 * This provides better UX by automatically dismissing the full-screen mobile menu
	 */
	function handleMobileLinkClick() {
		isMobileNavOpen = false;
	}
</script>

<!-- Main Navigation Header -->
<header
	role="navigation"
	class="text-muted-foreground bg-background sticky left-0 top-0 z-50 flex h-fit w-full items-center justify-center border-b"
	onmouseleave={handleHeaderMouseLeave}
>
	<!-- Expandable Navigation Container -->
	<nav
		class={clsx(
			"flex w-full flex-col items-center justify-center",
			isMobileNavOpen ? "min-h-screen md:min-h-fit" : ""
		)}
	>
		<!-- Primary Navigation Bar -->
		<div
			class="h-13 md:h-15 flex w-full max-w-[1300px] flex-row items-center justify-between px-4 md:px-16 lg:px-24"
		>
			<!-- Brand and Desktop Navigation -->
			<div class="flex flex-row items-center justify-center gap-2">
				<!-- Brand Logo -->
				<a href="/" aria-label="Home">
					<BrandLogoIconWithText />
				</a>

				<!-- Desktop Navigation Links (hidden on mobile/tablet) -->

				<NavLinkList
					links={desktopNavLinks}
					{activeDropdownId}
					onTriggerEnter={handleDropdownEnter}
					onTriggerLeave={handleDropdownLeave}
					currentPathname={$page.url.pathname}
					class="hidden flex-row gap-2 lg:flex"
					linkClass="cursor-pointer"
				/>
			</div>

			<!-- Right Side Navigation Actions -->
			<div class="flex flex-row items-center gap-4">
				<!-- Theme Toggle - Always visible across all breakpoints -->
				<LightSwitch />

				<!-- Desktop Auth CTAs (hidden on mobile) -->
				<div class="hidden flex-row gap-3 md:flex">
					<Button class="flex-1" variant="outline" href={loginLink.href}>
						{loginLink.label}
					</Button>
					<Button variant="outline" class="flex-1" href={scheduleDemoLink.href}>
						{scheduleDemoLink.label}
					</Button>
				</div>

				<!-- Mobile Navigation Toggle (hidden on desktop) -->
				<MobileNav bind:isMobileNavOpen class="lg:hidden" />
			</div>
		</div>

		<!-- Expandable Mobile/Tablet Navigation -->
		{#if isMobileNavOpen}
			<!-- Mobile Navigation (small screens) -->
			<div class="flex flex-1 flex-col justify-start gap-2 overflow-y-auto md:hidden">
				<!-- Mobile Navigation Links -->
				<div class="flex w-full flex-col items-center justify-start gap-3 py-4">
					{#each defaultLeafNavLinks as link (link.label)}
						<Button
							variant="ghost"
							href={link.href}
							onclick={handleMobileLinkClick}
							class={isLinkActive(link.href, $page.url.pathname) ? "text-primary" : ""}
						>
							{link.label}
						</Button>
					{/each}
				</div>

				<!-- Mobile Auth CTAs -->
				<div class="mt-3 flex h-fit w-full justify-between px-4">
					<Button class="mr-2 flex-1" variant="outline" href={loginLink.href} onclick={handleMobileLinkClick}>
						{loginLink.label}
					</Button>
					<Button class="ml-2 flex-1" href={scheduleDemoLink.href} onclick={handleMobileLinkClick}>
						{scheduleDemoLink.label}
					</Button>
				</div>
			</div>

			<!-- Tablet Navigation (medium screens) -->
			<NavLinkList
				links={topLevelLinks}
				{activeDropdownId}
				onTriggerEnter={handleDropdownEnter}
				onTriggerLeave={handleDropdownLeave}
				onMobileLinkClick={handleMobileLinkClick}
				currentPathname={$page.url.pathname}
				class="hidden w-full flex-row items-center justify-around py-4 text-sm md:flex"
				linkClass="cursor-pointer"
			/>
		{/if}
	</nav>
</header>
