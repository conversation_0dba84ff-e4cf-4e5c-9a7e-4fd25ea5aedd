<script lang="ts">
	import { <PERSON><PERSON> } from "../ui/button";
	import { NavChevronDownIcon } from "../icons/ui";
	import { hasChildren } from "$lib/custom-utils/index";
	import { isLinkActive } from "$lib/custom-utils/index";
	import type { NavLink } from "$types";

	/**
	 * Props interface for the navigation dropdown button component
	 */
	interface Props {
		link: NavLink;
		variant?: "default" | "ghost" | "outline";
		class?: string;
		activeDropdownId: string | null;
		onTriggerEnter: (linkId: string) => void;
		onTriggerLeave: () => void;
		onMobileLinkClick?: () => void;
		currentPathname?: string;
	}

	let {
		link,
		variant = "ghost",
		class: className = "",
		activeDropdownId,
		onTriggerEnter,
		onTriggerLeave,
		onMobileLinkClick,
		currentPathname
	}: Props = $props();

	/**
	 * Determines if this dropdown is currently active/open
	 */
	const isDropdownActive = $derived(activeDropdownId === link.label);

	/**
	 * <PERSON>les mouse enter event for dropdown trigger
	 */
	function handleMouseEnter() {
		if (hasChildren(link)) {
			onTriggerEnter(link.label);
		}
	}

	/**
	 * Handles mouse leave event for dropdown trigger
	 */
	function handleMouseLeave() {
		if (hasChildren(link)) {
			onTriggerLeave();
		}
	}

	/**
	 * Determines if this link is currently active
	 */
	const isActive = $derived(currentPathname ? isLinkActive(currentPathname, link.href) : false);

	/**
	 * Handles click events on dropdown links in mobile view
	 * Automatically closes the mobile navigation when a dropdown link is clicked
	 */
	function handleDropdownLinkClick() {
		// Only close mobile nav if we're in mobile view and have the handler
		if (onMobileLinkClick && window.innerWidth < 768) {
			onMobileLinkClick();
		}
	}
</script>

{#if !hasChildren(link)}
	<!-- Simple navigation link without dropdown -->
	<Button
		{variant}
		class={`cursor-pointer ${className} ${isActive ? "text-blackwhite" : ""}`}
		href={link.href}
	>
		{link.label}
	</Button>
{:else}
	<!-- Navigation link with dropdown functionality -->
	<Button
		{variant}
		class={`relative ${className}`}
		href={link.href}
		onclick={handleDropdownLinkClick}
		onmouseenter={handleMouseEnter}
		onmouseleave={handleMouseLeave}
	>
		<!-- Button content with chevron indicator -->
		<div class="flex flex-row items-center justify-center gap-1">
			{link.label}
			<NavChevronDownIcon />
		</div>

		<!-- Dropdown menu - only rendered when active -->
		{#if isDropdownActive}
			<div
				class="bg-background text-muted-foreground absolute left-1/2 top-full z-[100] flex -translate-x-1/2 flex-col gap-2 rounded-md border p-2 shadow-md"
			>
				{#each link.children as childLink (childLink.label)}
					<a
						href={childLink.href}
						onclick={handleDropdownLinkClick}
						class="hover:bg-muted rounded px-2 py-1 text-sm transition-colors"
					>
						{childLink.label}
					</a>
				{/each}
			</div>
		{/if}
	</Button>
{/if}
