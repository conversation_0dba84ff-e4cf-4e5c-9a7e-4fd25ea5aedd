<script lang="ts">
	import { navLinks, socialMediaLinks } from "$config/navLinks";
	import { getAllLeafLinks, hasChildren } from "$lib/custom-utils/index";
	import type { LeafNavLink } from "$types";
	import { BrandLogoIcon } from "../icons/brand";
	import { Button } from "../ui/button";

	type NavLinkGroup = {
		label: string;
		children: LeafNavLink[];
	};

	const productNavLinks: NavLinkGroup = {
		label: "Product",
		children: getAllLeafLinks([navLinks.find((link) => link.label === "Product")!])
	};
	const companyNavLinks: NavLinkGroup = {
		label: "Company",
		children: getAllLeafLinks([navLinks.find((link) => link.label === "Company")!])
	};
	const legalNavLinks: NavLinkGroup = {
		label: "Legal",
		children: getAllLeafLinks([navLinks.find((link) => link.label === "Legal")!])
	};
	const otherNavLinks: NavLinkGroup = {
		label: "Other",
		children: navLinks
			.filter((link) => !hasChildren(link))
			.filter((link) => link.label !== "Home")
			.map((link) => link as LeafNavLink)
	};

	const navLinkGroups = [productNavLinks, companyNavLinks, otherNavLinks, legalNavLinks];
</script>

<div class="flex h-fit w-full justify-center border-t">
	<footer
		class="text-muted-foreground flex h-fit w-full flex-col justify-center border-t px-3 text-xs md:max-w-[1200px] md:px-8 md:text-sm"
	>
		<div class="flex flex-col gap-12 py-12 md:flex-row md:justify-between md:pb-24">
			<!-- Socials -->
			<div class="flex flex-col gap-4 md:gap-6">
				<!-- Logo -->
				<div class="flex h-fit w-fit flex-row items-center justify-center gap-1">
					<BrandLogoIcon size={34} color="text-accent" />
					<div
						class="flex h-fit w-fit flex-row items-center justify-center text-2xl/loose font-bold tracking-wide md:text-3xl/loose"
					>
						<p class="text-accent">evo</p>
						<p class="text-primary">prof</p>
					</div>
				</div>

				<!-- Tagline -->
				<div class="text-muted-foreground md:text-md flex flex-col gap-1.5 text-sm md:gap-3">
					<p>Future-Proof student records</p>
					<p>and skill insights.</p>
				</div>

				<!-- Socials -->
				<div class="flex flex-row gap-2">
					{#each socialMediaLinks as socialMediaLink (socialMediaLink.href)}
						<Button variant="outline" href={socialMediaLink.href} class="rounded-xl">
							<socialMediaLink.icon class="size-4" />
						</Button>
					{/each}
				</div>
			</div>

			<!-- Nav Links -->
			<nav class="grid grid-cols-2 gap-10 md:grid-cols-4 md:gap-4">
				{#each navLinkGroups as navLinkGroup (navLinkGroup.label)}
					<div class="flex flex-col gap-5">
						<h6>
							{navLinkGroup.label}
						</h6>
						<div class="flex flex-col gap-2">
							{#each navLinkGroup.children as link (link.href)}
								<a
									class="text-foreground hover:mb-0.2 w-fit transition-all duration-200 hover:translate-x-1"
									href={link.href}>{link.label}</a
								>
							{/each}
						</div>
					</div>
				{/each}
			</nav>
		</div>

		<!-- Copyright -->
		<div class="flex flex-col border-t px-4 py-2 md:flex-row md:justify-between md:px-0">
			<p class="text-center">
				© 2025 <a href="https://evoprof.com" class="text-accent">evoprof</a>. All rights reserved.
			</p>
			<p class="text-center">
				Crafted by <a href="https://logicode.com" class="text-accent underline decoration-1"
					>Logicode</a
				>.
			</p>
		</div>
	</footer>
</div>
