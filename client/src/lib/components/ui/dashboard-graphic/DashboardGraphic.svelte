<script lang="ts">
	import { onMount } from 'svelte';
	import { Badge } from '$lib/components/ui/badge';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { Progress } from '$lib/components/ui/progress';

	// Animation state
	let mounted = false;

	// Mock data for the dashboard
	const skillsData = [
		{ name: 'Digital Literacy', progress: 78, color: 'hsl(var(--chart-1))' },
		{ name: 'Technical Skills', progress: 65, color: 'hsl(var(--chart-2))' },
		{ name: 'Soft Skills', progress: 82, color: 'hsl(var(--chart-3))' },
		{ name: 'Leadership', progress: 71, color: 'hsl(var(--chart-4))' }
	];

	const kpiData = [
		{ label: 'Total Students', value: '12,847', change: '+12%', trend: 'up' },
		{ label: 'Active Institutions', value: '156', change: '+8%', trend: 'up' },
		{ label: 'Completion Rate', value: '89.2%', change: '+5.3%', trend: 'up' },
		{ label: 'Skills Certified', value: '3,421', change: '+18%', trend: 'up' }
	];

	const institutionData = [
		{ name: 'Nairobi Tech', students: 1200, performance: 92 },
		{ name: 'Mombasa College', students: 980, performance: 87 },
		{ name: 'Kisumu Institute', students: 756, performance: 84 },
		{ name: 'Eldoret Academy', students: 634, performance: 89 },
		{ name: 'Nakuru School', students: 523, performance: 91 }
	];

	const recentActivities = [
		{ student: 'Sarah M.', action: 'Completed Digital Marketing Course', time: '2 hours ago', institution: 'Nairobi Tech' },
		{ student: 'John K.', action: 'Achieved Leadership Certification', time: '4 hours ago', institution: 'Mombasa College' },
		{ student: 'Grace W.', action: 'Started Data Analysis Program', time: '6 hours ago', institution: 'Kisumu Institute' },
		{ student: 'David O.', action: 'Completed Soft Skills Assessment', time: '8 hours ago', institution: 'Eldoret Academy' }
	];

	const regionData = [
		{ region: 'Central', students: 4200, institutions: 45 },
		{ region: 'Coast', students: 2800, institutions: 32 },
		{ region: 'Western', students: 2100, institutions: 28 },
		{ region: 'Rift Valley', students: 1900, institutions: 25 },
		{ region: 'Eastern', students: 1500, institutions: 20 },
		{ region: 'Nyanza', students: 347, institutions: 6 }
	];

	onMount(() => {
		mounted = true;
	});

	function getMaxValue(data: any[], key: string) {
		return Math.max(...data.map(item => item[key]));
	}
</script>

<div class="w-full max-w-7xl mx-auto p-6 space-y-6">
	<!-- Header Section -->
	<div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-8">
		<div class="space-y-2">
			<h2 class="text-2xl md:text-3xl font-semibold text-foreground">Skills Analytics Dashboard</h2>
			<p class="text-muted-foreground">Real-time insights into student progress and institutional performance</p>
		</div>
		<div class="flex items-center gap-2">
			<Badge variant="secondary" class="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
				Live Data
			</Badge>
			<Badge variant="outline">Last updated: 5 min ago</Badge>
		</div>
	</div>

	<!-- KPI Cards -->
	<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
		{#each kpiData as kpi, index}
			<Card class="relative overflow-hidden transition-all duration-500 hover:shadow-lg {mounted ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}"
				  style="transition-delay: {index * 100}ms">
				<CardContent class="p-6">
					<div class="flex items-center justify-between">
						<div class="space-y-2">
							<p class="text-sm font-medium text-muted-foreground">{kpi.label}</p>
							<p class="text-2xl font-bold text-foreground">{kpi.value}</p>
							<div class="flex items-center gap-1">
								<span class="text-xs text-green-600 dark:text-green-400 font-medium">{kpi.change}</span>
								<svg class="w-3 h-3 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
									<path fill-rule="evenodd" d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
								</svg>
							</div>
						</div>
						<div class="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
							<div class="w-6 h-6 rounded-full bg-primary/20"></div>
						</div>
					</div>
				</CardContent>
			</Card>
		{/each}
	</div>

	<!-- Main Dashboard Grid -->
	<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
		<!-- Skills Progress -->
		<Card class="lg:col-span-1 transition-all duration-700 {mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}"
			  style="transition-delay: 400ms">
			<CardHeader>
				<CardTitle class="text-lg font-semibold">Skills Development Progress</CardTitle>
			</CardHeader>
			<CardContent class="space-y-6">
				{#each skillsData as skill, index}
					<div class="space-y-2">
						<div class="flex justify-between items-center">
							<span class="text-sm font-medium text-foreground">{skill.name}</span>
							<span class="text-sm text-muted-foreground">{skill.progress}%</span>
						</div>
						<Progress
							value={mounted ? skill.progress : 0}
							class="h-2 transition-all duration-1000"
							style="transition-delay: {(index + 1) * 200}ms"
						/>
					</div>
				{/each}
			</CardContent>
		</Card>

		<!-- Institution Performance Chart -->
		<Card class="lg:col-span-2 transition-all duration-700 {mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}"
			  style="transition-delay: 600ms">
			<CardHeader>
				<CardTitle class="text-lg font-semibold">Top Performing Institutions</CardTitle>
			</CardHeader>
			<CardContent>
				<div class="space-y-4">
					{#each institutionData as institution, index}
						<div class="flex items-center gap-4">
							<div class="w-24 text-sm font-medium text-foreground truncate">{institution.name}</div>
							<div class="flex-1 space-y-1">
								<div class="flex justify-between text-xs text-muted-foreground">
									<span>{institution.students} students</span>
									<span>{institution.performance}% performance</span>
								</div>
								<div class="w-full bg-muted rounded-full h-2 overflow-hidden">
									<div
										class="h-full bg-gradient-to-r from-chart-1 to-chart-2 rounded-full transition-all duration-1000 ease-out"
										style="width: {mounted ? institution.performance : 0}%; transition-delay: {(index + 1) * 150}ms"
									></div>
								</div>
							</div>
						</div>
					{/each}
				</div>
			</CardContent>
		</Card>
	</div>

	<!-- Second Row: Activity Feed and Regional Distribution -->
	<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
		<!-- Recent Activities -->
		<Card class="transition-all duration-700 {mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}"
			  style="transition-delay: 800ms">
			<CardHeader>
				<CardTitle class="text-lg font-semibold">Recent Student Activities</CardTitle>
			</CardHeader>
			<CardContent>
				<div class="space-y-4">
					{#each recentActivities as activity}
						<div class="flex items-start gap-3 p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors duration-200">
							<div class="w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0"></div>
							<div class="flex-1 space-y-1">
								<div class="flex items-center justify-between">
									<span class="text-sm font-medium text-foreground">{activity.student}</span>
									<span class="text-xs text-muted-foreground">{activity.time}</span>
								</div>
								<p class="text-sm text-muted-foreground">{activity.action}</p>
								<Badge variant="outline" class="text-xs">{activity.institution}</Badge>
							</div>
						</div>
					{/each}
				</div>
			</CardContent>
		</Card>

		<!-- Regional Distribution -->
		<Card class="transition-all duration-700 {mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}"
			  style="transition-delay: 1000ms">
			<CardHeader>
				<CardTitle class="text-lg font-semibold">Regional Distribution</CardTitle>
			</CardHeader>
			<CardContent>
				<div class="space-y-4">
					{#each regionData as region, index}
						<div class="space-y-2">
							<div class="flex justify-between items-center">
								<span class="text-sm font-medium text-foreground">{region.region}</span>
								<div class="text-right">
									<div class="text-sm font-semibold text-foreground">{region.students.toLocaleString()}</div>
									<div class="text-xs text-muted-foreground">{region.institutions} institutions</div>
								</div>
							</div>
							<div class="w-full bg-muted rounded-full h-2 overflow-hidden">
								<div
									class="h-full bg-gradient-to-r from-chart-3 to-chart-4 rounded-full transition-all duration-1000 ease-out"
									style="width: {mounted ? (region.students / getMaxValue(regionData, 'students')) * 100 : 0}%; transition-delay: {(index + 1) * 200}ms"
								></div>
							</div>
						</div>
					{/each}
				</div>
			</CardContent>
		</Card>
	</div>

	<!-- Bottom Section: Trend Chart Simulation -->
	<Card class="transition-all duration-700 {mounted ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}"
		  style="transition-delay: 1200ms">
		<CardHeader>
			<CardTitle class="text-lg font-semibold">Skills Development Trends</CardTitle>
		</CardHeader>
		<CardContent>
			<div class="h-64 flex items-end justify-between gap-2 p-4 bg-gradient-to-t from-muted/20 to-transparent rounded-lg">
				{#each Array(12) as _, index}
					<div class="flex flex-col items-center gap-2 flex-1">
						<div
							class="w-full bg-gradient-to-t from-primary to-primary/60 rounded-t transition-all duration-1000 ease-out"
							style="height: {mounted ? Math.random() * 80 + 20 : 0}%; transition-delay: {index * 100}ms"
						></div>
						<span class="text-xs text-muted-foreground">
							{['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][index]}
						</span>
					</div>
				{/each}
			</div>
			<div class="mt-4 flex items-center justify-center gap-6 text-sm text-muted-foreground">
				<div class="flex items-center gap-2">
					<div class="w-3 h-3 rounded-full bg-primary"></div>
					<span>Skills Completion Rate</span>
				</div>
				<div class="flex items-center gap-2">
					<div class="w-3 h-3 rounded-full bg-chart-2"></div>
					<span>Student Engagement</span>
				</div>
			</div>
		</CardContent>
	</Card>
</div>
