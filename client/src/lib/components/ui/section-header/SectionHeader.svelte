<script lang="ts">
	/**
	 * HeaderWithAnimatedBackground Component
	 *
	 * A reusable header component featuring:
	 * - Dynamic color theming via props
	 * - Animated geometric background pattern
	 * - Soft triangular transparency mask
	 * - Full viewport width background coverage
	 * - Consistent styling with section badges
	 */

	let {
		sectionName = "Section Name",
		headline = "Section Headline",
		subheadline = "Section Subheadline",
		color = "#00AD09"
	}: {
		sectionName: string;
		headline: string;
		subheadline: string;
		color: string;
	} = $props();
</script>

<!--
	Header container with animated background
	- Uses CSS custom properties for dynamic color theming
	- Applies full-width animated background via ::after pseudo-element
-->
<header
	class="header-with-border flex flex-col items-center gap-1.5 pt-12 text-center lg:pt-24"
	style="--primary-color: {color};"
>
	<!-- Section Badge - matches background color theme -->
	<div
		class="subsection-subitem-text py-0.1 flex h-fit w-fit items-center justify-center rounded-full border px-2 font-[400] tracking-tighter"
		style="color: {color}; background-color: color-mix(in srgb, {color} 20%, transparent);"
	>
		{sectionName}
	</div>

	<!-- Main Headline -->
	<h2 class="section-headline-text">{headline}</h2>

	<!-- Subheadline with HTML support -->
	<!-- eslint-disable-next-line svelte/no-at-html-tags -->
	<p class="">{@html subheadline}</p>
</header>

<style>
	.header-with-border {
		position: relative;

		/* Pattern size control - adjust this value to scale the entire pattern */
		--s: 10px;

		/* Dynamic color variations using the passed-in color prop */
		/* Primary color at 50% base transparency, with additional opacity variations */
		--c1: color-mix(in srgb, var(--primary-color) 30%, transparent);
		--c2: color-mix(in srgb, var(--primary-color) 15%, transparent);

		/* Background pattern helper variables for clean, maintainable code */
		--_s: calc(2 * var(--s)) calc(2 * var(--s));
		--_g: var(--_s) conic-gradient(at 40% 40%, #0000 75%, var(--c1) 0);
		--_p: var(--_s) conic-gradient(at 20% 20%, #0000 75%, var(--c2) 0);
	}

	/* Top border - spans full viewport width */
	.header-with-border::before {
		content: "";
		position: absolute;
		top: 0;
		left: 50%;
		width: 100vw;
		height: 1px;
		background-color: var(--color-border);
		margin-left: -50vw;
	}

	/* Animated background pattern - spans full viewport width */
	.header-with-border::after {
		content: "";
		position: absolute;
		top: 0;
		left: 50%;
		width: 100vw;
		height: 100%;
		margin-left: -50vw;
		z-index: -1;

		/* Advanced geometric pattern using multiple layered conic gradients */
		/* Creates a complex, organic-looking animated texture */
		background:
			calc(0.9 * var(--s)) calc(0.9 * var(--s)) / var(--_p),
			calc(-0.1 * var(--s)) calc(-0.1 * var(--s)) / var(--_p),
			calc(0.7 * var(--s)) calc(0.7 * var(--s)) / var(--_g),
			calc(-0.3 * var(--s)) calc(-0.3 * var(--s)) / var(--_g),
			conic-gradient(from 90deg at 20% 20%, var(--c2) 25%, var(--c1) 0) 0 0 / var(--s) var(--s);

		/* Complex multi-step animation with varied timing */
		animation: bg-pattern-move 8s infinite;

		/* Soft triangular transparency mask */
		/* Creates fade effect: most opaque at top center, transparent at edges */
		/* Elliptical shape creates natural triangular appearance */
		-webkit-mask: radial-gradient(
			ellipse 50% 100% at center 20%,
			rgba(0, 0, 0, 0.6) 0%,
			rgba(0, 0, 0, 0.4) 30%,
			rgba(0, 0, 0, 0.2) 60%,
			rgba(0, 0, 0, 0) 100%
		);
		mask: radial-gradient(
			ellipse 50% 100% at center 20%,
			rgba(0, 0, 0, 0.6) 0%,
			rgba(0, 0, 0, 0.4) 30%,
			rgba(0, 0, 0, 0.2) 60%,
			rgba(0, 0, 0, 0) 100%
		);
	}

	/*
	 * Advanced background pattern animation keyframes
	 * Creates complex, organic movement with multiple phases
	 * Each layer moves independently for dynamic depth effect
	 */
	@keyframes bg-pattern-move {
		0% {
			background-position:
				calc(0.9 * var(--s)) calc(0.9 * var(--s)),
				calc(-0.1 * var(--s)) calc(-0.1 * var(--s)),
				calc(0.7 * var(--s)) calc(0.7 * var(--s)),
				calc(-0.3 * var(--s)) calc(-0.3 * var(--s)),
				0 0;
		}
		25% {
			background-position:
				calc(1.9 * var(--s)) calc(0.9 * var(--s)),
				calc(-1.1 * var(--s)) calc(-0.1 * var(--s)),
				calc(1.7 * var(--s)) calc(0.7 * var(--s)),
				calc(-1.3 * var(--s)) calc(-0.3 * var(--s)),
				0 0;
		}
		50% {
			background-position:
				calc(1.9 * var(--s)) calc(-0.1 * var(--s)),
				calc(-1.1 * var(--s)) calc(0.9 * var(--s)),
				calc(1.7 * var(--s)) calc(-0.3 * var(--s)),
				calc(-1.3 * var(--s)) calc(0.7 * var(--s)),
				0 0;
		}
		75% {
			background-position:
				calc(2.9 * var(--s)) calc(-0.1 * var(--s)),
				calc(-2.1 * var(--s)) calc(0.9 * var(--s)),
				calc(2.7 * var(--s)) calc(-0.3 * var(--s)),
				calc(-2.3 * var(--s)) calc(0.7 * var(--s)),
				0 0;
		}
		100% {
			background-position:
				calc(2.9 * var(--s)) calc(-1.1 * var(--s)),
				calc(-2.1 * var(--s)) calc(1.9 * var(--s)),
				calc(2.7 * var(--s)) calc(-1.3 * var(--s)),
				calc(-2.3 * var(--s)) calc(1.7 * var(--s)),
				0 0;
		}
	}
</style>
