<script lang="ts" module>
	import { type VariantProps, tv } from "tailwind-variants";

	export const accordionVariants = tv({
		base: "w-full",
		variants: {
			variant: {
				default: "border-border divide-y divide-border rounded-md border",
				ghost: "divide-y divide-border",
				noBorderGap: "space-y-4"
			}
		},
		defaultVariants: {
			variant: "default"
		}
	});

	export type AccordionVariant = VariantProps<typeof accordionVariants>["variant"];
	export type AccordionType = "single" | "multiple";
</script>

<script lang="ts">
	import { cn, type WithElementRef } from "$lib/utils.js";
	import type { HTMLAttributes } from "svelte/elements";
	import { setContext } from "svelte";

	let {
		ref = $bindable(null),
		class: className,
		variant = "default",
		type = "single",
		value = $bindable(type === "single" ? "" : []),
		defaultValue = type === "single" ? "" : [],
		collapsible = true,
		disabled = false,
		children,
		...restProps
	}: WithElementRef<HTMLAttributes<HTMLDivElement>> & {
		variant?: AccordionVariant;
		type?: AccordionType;
		value?: string | string[];
		defaultValue?: string | string[];
		collapsible?: boolean;
		disabled?: boolean;
	} = $props();

	// Initialize value if not provided
	if (value === undefined) {
		value = defaultValue;
	}

	// Context for child components
	const accordionContext = {
		get type() {
			return type;
		},
		get value() {
			return value;
		},
		get collapsible() {
			return collapsible;
		},
		get disabled() {
			return disabled;
		},
		updateValue: (itemValue: string) => {
			if (disabled) return;

			if (type === "single") {
				// Single mode: toggle the item or set it if different
				if (value === itemValue && collapsible) {
					value = "";
				} else {
					value = itemValue;
				}
			} else {
				// Multiple mode: toggle item in array
				const currentArray = Array.isArray(value) ? value : [];
				const index = currentArray.indexOf(itemValue);
				if (index > -1) {
					value = currentArray.filter((v) => v !== itemValue);
				} else {
					value = [...currentArray, itemValue];
				}
			}
		},
		isOpen: (itemValue: string) => {
			if (type === "single") {
				return value === itemValue;
			} else {
				return Array.isArray(value) && value.includes(itemValue);
			}
		}
	};

	setContext("accordion", accordionContext);
</script>

<div
	bind:this={ref}
	data-slot="accordion"
	class={cn(accordionVariants({ variant }), className)}
	{...restProps}
>
	{@render children?.()}
</div>
