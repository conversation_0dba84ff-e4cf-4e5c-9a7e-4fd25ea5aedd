import Root from "./accordion.svelte";
import Item from "./accordion-item.svelte";
import Trigger from "./accordion-trigger.svelte";
import Content from "./accordion-content.svelte";

export {
	accordionVariants,
	type AccordionVariant,
	type AccordionType,
} from "./accordion.svelte";

export {
	accordionItemVariants,
	type AccordionItemVariant,
} from "./accordion-item.svelte";

export {
	accordionTriggerVariants,
	type AccordionTriggerVariant,
} from "./accordion-trigger.svelte";

export {
	accordionContentVariants,
	type AccordionContentVariant,
} from "./accordion-content.svelte";

export {
	Root,
	Item,
	Trigger,
	Content,
	//
	Root as Accordion,
	Item as AccordionItem,
	Trigger as AccordionTrigger,
	Content as AccordionContent,
};
