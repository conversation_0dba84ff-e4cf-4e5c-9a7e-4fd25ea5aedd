<script lang="ts" module>
	import { tv, type VariantProps } from "tailwind-variants";

	export const accordionTriggerVariants = tv({
		base: "flex flex-row w-full items-center justify-between py-4 transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]_svg]:rotate-45 page-text font-[500] text-blackwhite",
		variants: {
			variant: {
				default: "px-4",
				ghost: "px-0"
			}
		},
		defaultVariants: {
			variant: "default"
		}
	});

	export type AccordionTriggerVariant = VariantProps<typeof accordionTriggerVariants>["variant"];
</script>

<script lang="ts">
	import { cn, type WithElementRef } from "$lib/utils.js";
	import type { HTMLButtonAttributes } from "svelte/elements";
	import { getContext } from "svelte";
	import PlusIcon from "$lib/components/icons/ui/PlusIcon.svelte";

	let {
		ref = $bindable(null),
		class: className,
		variant = "default",
		children,
		...restProps
	}: WithElementRef<HTMLButtonAttributes> & {
		variant?: AccordionTriggerVariant;
	} = $props();

	const itemContext = getContext<{
		value: string;
		disabled: boolean;
		isOpen: boolean;
		toggle: () => void;
	}>("accordion-item");

	if (!itemContext) {
		throw new Error("AccordionTrigger must be used within an AccordionItem component");
	}

	function handleClick() {
		itemContext.toggle();
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === "Enter" || event.key === " ") {
			event.preventDefault();
			itemContext.toggle();
		}
	}
</script>

<button
	bind:this={ref}
	type="button"
	data-slot="accordion-trigger"
	data-state={itemContext.isOpen ? "open" : "closed"}
	id="accordion-trigger-{itemContext.value}"
	aria-expanded={itemContext.isOpen}
	aria-controls="accordion-content-{itemContext.value}"
	disabled={itemContext.disabled}
	class={cn(accordionTriggerVariants({ variant }), className)}
	onclick={handleClick}
	onkeydown={handleKeydown}
	{...restProps}
>
	{@render children?.()}
	<div class="bg-border text-muted-foreground shrink-0 rounded-lg p-1">
		<PlusIcon class="shrink-0 transition-transform duration-200" />
	</div>
</button>
