<script lang="ts" module>
	import { tv, type VariantProps } from "tailwind-variants";

	export const accordionItemVariants = tv({
		base: "w-full min-w-0",
		variants: {
			variant: {
				default: "",
				ghost: ""
			}
		},
		defaultVariants: {
			variant: "default"
		}
	});

	export type AccordionItemVariant = VariantProps<typeof accordionItemVariants>["variant"];
</script>

<script lang="ts">
	import { cn, type WithElementRef } from "$lib/utils.js";
	import type { HTMLAttributes } from "svelte/elements";
	import { getContext, setContext } from "svelte";

	let {
		ref = $bindable(null),
		class: className,
		variant = "default",
		value,
		disabled = false,
		children,
		...restProps
	}: WithElementRef<HTMLAttributes<HTMLDivElement>> & {
		variant?: AccordionItemVariant;
		value: string;
		disabled?: boolean;
	} = $props();

	const accordionContext = getContext<{
		type: "single" | "multiple";
		value: string | string[];
		collapsible: boolean;
		disabled: boolean;
		updateValue: (value: string) => void;
		isOpen: (value: string) => boolean;
	}>("accordion");

	if (!accordionContext) {
		throw new Error("AccordionItem must be used within an Accordion component");
	}

	// Item context for trigger and content
	const itemContext = {
		get value() {
			return value;
		},
		get disabled() {
			return disabled || accordionContext.disabled;
		},
		get isOpen() {
			return accordionContext.isOpen(value);
		},
		toggle: () => {
			if (!disabled && !accordionContext.disabled) {
				accordionContext.updateValue(value);
			}
		}
	};

	setContext("accordion-item", itemContext);
</script>

<div
	bind:this={ref}
	data-slot="accordion-item"
	data-state={itemContext.isOpen ? "open" : "closed"}
	data-disabled={itemContext.disabled ? "" : undefined}
	class={cn(accordionItemVariants({ variant }), className)}
	{...restProps}
>
	{@render children?.()}
</div>
