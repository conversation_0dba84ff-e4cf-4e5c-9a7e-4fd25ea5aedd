<script lang="ts" module>
	import { tv, type VariantProps } from "tailwind-variants";

	export const accordionContentVariants = tv({
		base: "w-full overflow-hidden text-sm transition-all duration-200 ease-out text-page/normal text-muted-foreground md:text-pagemd font-[350] pt-4",
		variants: {
			variant: {
				default: "",
				ghost: ""
			}
		},
		defaultVariants: {
			variant: "default"
		}
	});

	export type AccordionContentVariant = VariantProps<typeof accordionContentVariants>["variant"];
</script>

<script lang="ts">
	import { cn, type WithElementRef } from "$lib/utils.js";
	import type { HTMLAttributes } from "svelte/elements";
	import { getContext } from "svelte";
	import { slide } from "svelte/transition";

	let {
		ref = $bindable(null),
		class: className,
		variant = "default",
		children,
		...restProps
	}: WithElementRef<HTMLAttributes<HTMLDivElement>> & {
		variant?: AccordionContentVariant;
	} = $props();

	const itemContext = getContext<{
		value: string;
		disabled: boolean;
		isOpen: boolean;
		toggle: () => void;
	}>("accordion-item");

	if (!itemContext) {
		throw new Error("AccordionContent must be used within an AccordionItem component");
	}
</script>

{#if itemContext.isOpen}
	<div
		bind:this={ref}
		data-slot="accordion-content"
		data-state="open"
		id="accordion-content-{itemContext.value}"
		role="region"
		aria-labelledby="accordion-trigger-{itemContext.value}"
		class={cn(accordionContentVariants({ variant }), className)}
		transition:slide={{ duration: 200 }}
		{...restProps}
	>
		<div class="w-full px-4 pb-4 pt-0">
			{@render children?.()}
		</div>
	</div>
{/if}
