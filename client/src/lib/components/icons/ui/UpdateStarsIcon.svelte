<script lang="ts">
	import type { HTMLAttributes } from "svelte/elements";

	let { ...props }: HTMLAttributes<SVGElement> = $props();
</script>

<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" {...props}>
	<path
		fill="currentColor"
		d="M7.846 8.822c.427-1.027 1.881-1.027 2.308 0l1.214 2.919l3.151.252c1.108.09 1.558 1.472.713 2.195l-2.4 2.057l.733 3.075c.258 1.081-.918 1.936-1.867 1.357L9 19.029l-2.698 1.648c-.949.58-2.125-.276-1.867-1.357l.733-3.075l-2.4-2.057c-.845-.723-.395-2.106.713-2.195l3.15-.252zM9 9.953l-1.042 2.505a1.25 1.25 0 0 1-1.054.766L4.2 13.44l2.06 1.765c.356.305.512.784.403 1.24l-.63 2.638l2.315-1.414a1.25 1.25 0 0 1 1.304 0l2.315 1.414l-.63-2.638a1.25 1.25 0 0 1 .403-1.24l2.06-1.765l-2.704-.216a1.25 1.25 0 0 1-1.054-.766z"
	/>
	<path
		fill="currentColor"
		d="M13.538 3.11a.5.5 0 0 1 .923 0l.48 1.152a.5.5 0 0 0 .421.306l1.244.1a.5.5 0 0 1 .285.878l-.947.812a.5.5 0 0 0-.162.495l.29 1.214a.5.5 0 0 1-.747.542l-1.065-.65a.5.5 0 0 0-.52 0l-1.065.65a.5.5 0 0 1-.747-.542l.289-1.214a.5.5 0 0 0-.161-.495l-.947-.812a.5.5 0 0 1 .285-.878l1.243-.1a.5.5 0 0 0 .422-.306zm5.009 6.86a.5.5 0 0 1 .906 0l.425.91a.5.5 0 0 0 .241.242l.91.425a.5.5 0 0 1 0 .906l-.91.425a.5.5 0 0 0-.241.241l-.425.91a.5.5 0 0 1-.906 0l-.425-.91a.5.5 0 0 0-.242-.241l-.91-.425a.5.5 0 0 1 0-.906l.91-.425a.5.5 0 0 0 .242-.241z"
		opacity="0.5"
	/>
</svg>
