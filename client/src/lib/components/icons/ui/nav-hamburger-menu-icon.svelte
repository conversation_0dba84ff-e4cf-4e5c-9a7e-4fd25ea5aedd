<script lang="ts">
	import gsap from "gsap";
	import { MorphSVGPlugin } from "gsap/MorphSVGPlugin";
	import type { HTMLAttributes } from "svelte/elements";

	let { isMobileNavOpen, ...props }: { isMobileNavOpen: boolean } & HTMLAttributes<SVGElement> =
		$props();

	const spring = "elastic.out(0.3, 0.2)";
	const duration = 1;

	function path1(element: SVGPathElement) {
		gsap.registerPlugin(MorphSVGPlugin);
		if (isMobileNavOpen) {
			gsap.to(element, {
				duration,
				morphSVG: "M3.76 3.76L20.24 20.24",
				ease: spring
			});
		} else {
			gsap.to(element, {
				duration,
				morphSVG: "M24 8H0",
				ease: spring
			});
		}
	}

	function path2(element: SVGPathElement) {
		if (isMobileNavOpen) {
			gsap.to(element, {
				duration,
				morphSVG: "M3.76 20.24L20.24 3.76",
				ease: spring
			});
		} else {
			gsap.to(element, {
				duration,
				morphSVG: "M24 16H0",
				ease: spring
			});
		}
	}
</script>

<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" {...props}>
	<g fill="none" stroke="currentColor" stroke-linecap="round" stroke-width="1.5">
		<path {@attach path1} d="M24 8H0" />
		<path {@attach path2} d="M24 16H0" />
	</g>
</svg>
