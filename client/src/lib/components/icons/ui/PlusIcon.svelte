<script lang="ts">
	import type { HTMLAttributes } from "svelte/elements";

	let { ...props }: HTMLAttributes<SVGElement> = $props();
</script>

<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 36 36" {...props}>
	<path
		fill="currentColor"
		d="M30 17H19V6a1 1 0 1 0-2 0v11H6a1 1 0 0 0-1 1a.91.91 0 0 0 1 .94h11V30a1 1 0 1 0 2 0V19h11a1 1 0 0 0 1-1a1 1 0 0 0-1-1"
		class="clr-i-outline clr-i-outline-path-1"
	/>
	<path fill="none" d="M0 0h36v36H0z" />
</svg>
