<!--
	Dashboard Page - Protected Route

	This is a simple dashboard page to test the authentication flow.
	It displays user information and provides a logout button.
	This page should only be accessible to authenticated users.
-->

<script lang="ts">
	import { Button } from "$lib/components/ui/button";
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from "$lib/components/ui/card";
	import { Badge } from "$lib/components/ui/badge";
	import { getAuthState } from "$lib/stores/auth.svelte";
	import { toast } from "svelte-sonner";

	// Get auth state from context
	const authState = getAuthState();

	/**
	 * Handle user logout
	 */
	async function handleLogout() {
		await authState.logout();
		toast.success("Logged out successfully");
	}

	/**
	 * Format date for display
	 */
	function formatDate(dateString: string | null): string {
		if (!dateString) return "Never";
		return new Date(dateString).toLocaleDateString("en-US", {
			year: "numeric",
			month: "long",
			day: "numeric",
			hour: "2-digit",
			minute: "2-digit"
		});
	}
</script>

<svelte:head>
	<title>Dashboard - Evoprof</title>
	<meta name="description" content="User dashboard for Evoprof platform" />
</svelte:head>

<div class="min-h-screen bg-gray-50 py-8">
	<div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
		<!-- Header -->
		<div class="mb-8">
			<div class="flex items-center justify-between">
				<div>
					<h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
					<p class="mt-2 text-gray-600">Welcome back to Evoprof!</p>
				</div>
				<Button variant="outline" onclick={handleLogout}>Logout</Button>
			</div>
		</div>

		{#if authState.user}
			<!-- User Information Card -->
			<div class="mb-8">
				<Card>
					<CardHeader>
						<CardTitle>User Profile</CardTitle>
						<CardDescription>Your account information and details</CardDescription>
					</CardHeader>
					<CardContent class="space-y-4">
						<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
							<div>
								<span class="text-sm font-medium text-gray-500">Username</span>
								<p class="text-lg font-semibold">{authState.user.username}</p>
							</div>
							<div>
								<span class="text-sm font-medium text-gray-500">Email</span>
								<p class="text-lg">{authState.user.email}</p>
							</div>
							<div>
								<span class="text-sm font-medium text-gray-500">User ID</span>
								<p class="font-mono text-sm text-gray-600">{authState.user.id}</p>
							</div>
							<div>
								<span class="text-sm font-medium text-gray-500">Account Status</span>
								<div class="flex items-center gap-2">
									<Badge variant={authState.user.is_active ? "default" : "destructive"}>
										{authState.user.is_active ? "Active" : "Inactive"}
									</Badge>
									{#if authState.user.is_staff}
										<Badge variant="secondary">Staff</Badge>
									{/if}
								</div>
							</div>
							<div>
								<span class="text-sm font-medium text-gray-500">Last Login</span>
								<p class="text-sm">{formatDate(authState.user.last_login)}</p>
							</div>
							<div>
								<span class="text-sm font-medium text-gray-500">Account Created</span>
								<p class="text-sm">{formatDate(authState.user.created_at)}</p>
							</div>
						</div>

						{#if authState.user.user_role}
							<div class="border-t pt-4">
								<span class="text-sm font-medium text-gray-500">Role & Permissions</span>
								<div class="mt-2">
									<Badge variant="outline" class="mb-2">
										{authState.user.user_role.role_name}
									</Badge>
									{#if authState.user.user_role.permissions.length > 0}
										<div class="mt-2">
											<p class="mb-2 text-sm text-gray-600">Permissions:</p>
											<div class="flex flex-wrap gap-1">
												{#each authState.user.user_role.permissions as permission}
													<Badge variant="secondary" class="text-xs">
														{permission.permission_name}
													</Badge>
												{/each}
											</div>
										</div>
									{:else}
										<p class="mt-2 text-sm text-gray-500">No specific permissions assigned</p>
									{/if}
								</div>
							</div>
						{:else}
							<div class="border-t pt-4">
								<p class="text-sm text-gray-500">No role assigned</p>
							</div>
						{/if}
					</CardContent>
				</Card>
			</div>

			<!-- Authentication Status Card -->
			<div class="mb-8">
				<Card>
					<CardHeader>
						<CardTitle>Authentication Status</CardTitle>
						<CardDescription>Current session and token information</CardDescription>
					</CardHeader>
					<CardContent class="space-y-4">
						<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
							<div>
								<span class="text-sm font-medium text-gray-500">Authentication Status</span>
								<Badge variant={authState.isAuthenticated ? "default" : "destructive"}>
									{authState.isAuthenticated ? "Authenticated" : "Not Authenticated"}
								</Badge>
							</div>
							<div>
								<span class="text-sm font-medium text-gray-500">Session Active</span>
								<Badge variant={authState.user ? "default" : "destructive"}>
									{authState.user ? "Active" : "Inactive"}
								</Badge>
							</div>
						</div>

						<div>
							<span class="text-sm font-medium text-gray-500">Session Information</span>
							<p class="text-sm text-gray-600">Authentication managed via secure cookies</p>
						</div>
					</CardContent>
				</Card>
			</div>

			<!-- Quick Actions -->
			<div class="grid grid-cols-1 gap-4 md:grid-cols-3">
				<Card>
					<CardContent class="p-6 text-center">
						<h3 class="mb-2 font-semibold">Profile Settings</h3>
						<p class="mb-4 text-sm text-gray-600">Update your profile information</p>
						<Button variant="outline" class="w-full" disabled>Coming Soon</Button>
					</CardContent>
				</Card>

				<Card>
					<CardContent class="p-6 text-center">
						<h3 class="mb-2 font-semibold">Security</h3>
						<p class="mb-4 text-sm text-gray-600">Manage your account security</p>
						<Button variant="outline" class="w-full" disabled>Coming Soon</Button>
					</CardContent>
				</Card>

				<Card>
					<CardContent class="p-6 text-center">
						<h3 class="mb-2 font-semibold">Support</h3>
						<p class="mb-4 text-sm text-gray-600">Get help and support</p>
						<Button variant="outline" class="w-full" href="/contact">Contact Support</Button>
					</CardContent>
				</Card>
			</div>
		{:else}
			<!-- Not authenticated state -->
			<Card>
				<CardContent class="p-8 text-center">
					<h2 class="mb-4 text-xl font-semibold">Authentication Required</h2>
					<p class="mb-6 text-gray-600">You need to be logged in to access this page.</p>
					<Button href="/login">Go to Login</Button>
				</CardContent>
			</Card>
		{/if}
	</div>
</div>
