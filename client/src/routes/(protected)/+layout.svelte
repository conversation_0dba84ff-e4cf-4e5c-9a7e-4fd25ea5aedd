<!--
	Protected Routes Layout

	This layout wraps all protected routes and ensures that only authenticated
	users can access them. It provides automatic redirects to the login page
	for unauthenticated users and handles loading states.
-->

<script lang="ts">
	import type { Snippet } from "svelte";
	import { browser } from "$app/environment";
	import { goto } from "$app/navigation";
	import { page } from "$app/stores";
	import { getAuthState } from "$lib/stores/auth.svelte";
	import { onMount } from "svelte";

	let { children }: { children: Snippet<[]> } = $props();

	// Get auth state from context
	const authState = getAuthState();

	// Track if we've completed the initial auth check
	let authCheckComplete = $state(false);

	/**
	 * Check authentication status and redirect if necessary
	 */
	function checkAuth() {
		if (!browser) return;

		// If not authenticated, redirect to login with return URL
		if (!authState.isAuthenticated && !authState.isLoading) {
			const returnUrl = encodeURIComponent($page.url.pathname + $page.url.search);
			goto(`/login?redirectTo=${returnUrl}`);
			return;
		}

		authCheckComplete = true;
	}

	/**
	 * Initialize authentication check when component mounts
	 */
	onMount(() => {
		// Check auth immediately since state is now initialized from server
		checkAuth();
	});

	/**
	 * Watch for changes in authentication state
	 */
	$effect(() => {
		if (browser) {
			checkAuth();
		}
	});
</script>

{#if authState.isLoading || !authCheckComplete}
	<!-- Loading state while checking authentication -->
	<div class="flex min-h-screen items-center justify-center">
		<div class="text-center">
			<div
				class="border-primary mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"
			></div>
			<p class="text-gray-600">Checking authentication...</p>
		</div>
	</div>
{:else if authState.isAuthenticated}
	<!-- User is authenticated, show the protected content -->
	{@render children()}
{:else}
	<!-- User is not authenticated, show fallback (shouldn't normally be seen due to redirect) -->
	<div class="flex min-h-screen items-center justify-center">
		<div class="text-center">
			<h1 class="mb-4 text-2xl font-bold text-gray-900">Access Denied</h1>
			<p class="mb-6 text-gray-600">You need to be logged in to access this page.</p>
			<a
				href="/login"
				class="bg-primary hover:bg-primary/90 focus:ring-primary inline-flex items-center rounded-md border border-transparent px-4 py-2 text-sm font-medium text-white focus:ring-2 focus:ring-offset-2 focus:outline-none"
			>
				Go to Login
			</a>
		</div>
	</div>
{/if}
