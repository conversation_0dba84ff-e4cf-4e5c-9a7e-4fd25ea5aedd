/**
 * Root layout server load function
 *
 * This function runs on the server for every page request and is responsible
 * for reading authentication cookies and providing initial auth state to the client.
 * This ensures that the authentication state is available immediately on page load
 * and prevents hydration mismatches.
 */

import type { LayoutServerLoad } from './$types';
import type { User } from '$types/auth';

/**
 * Load function that runs on every page request
 * 
 * Reads authentication cookies and provides initial user data to the client.
 * This allows the auth state to be initialized correctly on both server and client.
 */
export const load: LayoutServerLoad = async ({ cookies }) => {
	// Read authentication cookies
	const accessToken = cookies.get('access_token');
	const refreshToken = cookies.get('refresh_token');
	const userData = cookies.get('user_data');

	// Initialize user data
	let user: User | null = null;

	// If we have all required auth data, parse the user
	if (accessToken && refreshToken && userData) {
		try {
			user = JSON.parse(userData) as User;
		} catch (error) {
			console.error('Failed to parse user data from cookie:', error);
			// Clear corrupted cookies
			cookies.delete('access_token', { path: '/' });
			cookies.delete('refresh_token', { path: '/' });
			cookies.delete('user_data', { path: '/' });
		}
	}

	// Return the initial auth state
	return {
		user
	};
};
