<script lang="ts">
	import type { Snippet } from "svelte";
	import "../app.css";
	import { ModeWatcher } from "mode-watcher";
	import { setAuthState } from "$lib/stores/auth.svelte";
	import type { LayoutData } from "./$types";

	let { children, data }: { children: Snippet<[]>; data: LayoutData } = $props();

	// Set up authentication context with server-provided user data
	setAuthState(data.user);
</script>

<ModeWatcher />
<main class="page-text font-sans">
	{@render children()}
</main>
