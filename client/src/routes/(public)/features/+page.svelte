<script lang="ts">
	/**
	 * Features Page Component
	 *
	 * Comprehensive showcase of Evoprof's capabilities organized into logical sections:
	 * - Core platform features for skill tracking and assessment
	 * - Advanced AI and analytics capabilities
	 * - Integration and interoperability features
	 * - Security, compliance, and data protection
	 * - Reporting and insights dashboard features
	 *
	 * Design follows established patterns with alternating animated backgrounds
	 * between sections to maintain visual interest without overwhelming users.
	 */

	import { Button } from "$lib/components/ui/button";
	import SectionHeader from "$lib/components/ui/section-header/SectionHeader.svelte";
	import * as Card from "$lib/components/ui/card";
	import { Badge } from "$lib/components/ui/badge";

	// Core Features - Primary platform capabilities
	const coreFeatures = [
		{
			title: "Student Skill Tracking",
			description: "Comprehensive tracking of student progress across multiple skill domains with real-time updates and milestone recognition.",
			icon: "📈",
			category: "Core"
		},
		{
			title: "Institutional Dashboards",
			description: "Customized dashboards for schools, educators, and administrators with role-based access and personalized insights.",
			icon: "📊",
			category: "Core"
		},
		{
			title: "Bulk Student Onboarding",
			description: "Streamlined process for institutions to onboard hundreds of students simultaneously with automated credential generation.",
			icon: "👥",
			category: "Core"
		},
		{
			title: "Curriculum Alignment",
			description: "Skills frameworks that align with national curricula and international standards for consistent assessment.",
			icon: "📚",
			category: "Core"
		},
		{
			title: "Performance Analytics",
			description: "Deep insights into student performance patterns, learning gaps, and improvement opportunities.",
			icon: "🎯",
			category: "Core"
		},
		{
			title: "Progress Reporting",
			description: "Automated generation of detailed progress reports for students, parents, and institutional stakeholders.",
			icon: "📋",
			category: "Core"
		}
	];

	// Advanced Features - AI and sophisticated capabilities
	const advancedFeatures = [
		{
			title: "AI-Powered Skill Assessment",
			description: "Machine learning algorithms that analyze performance patterns and provide personalized learning recommendations.",
			icon: "🤖",
			category: "AI & Analytics"
		},
		{
			title: "Predictive Analytics",
			description: "Advanced forecasting of student outcomes and early identification of at-risk learners for timely intervention.",
			icon: "🔮",
			category: "AI & Analytics"
		},
		{
			title: "Intelligent Recommendations",
			description: "Personalized skill development pathways and resource suggestions based on individual learning patterns.",
			icon: "💡",
			category: "AI & Analytics"
		},
		{
			title: "Pattern Recognition",
			description: "Automated detection of learning patterns, bottlenecks, and opportunities across student populations.",
			icon: "🧠",
			category: "AI & Analytics"
		},
		{
			title: "Adaptive Learning Paths",
			description: "Dynamic adjustment of learning trajectories based on real-time performance data and skill mastery levels.",
			icon: "🛤️",
			category: "AI & Analytics"
		},
		{
			title: "Competency Mapping",
			description: "Sophisticated mapping of skills to career pathways and industry requirements for future-ready education.",
			icon: "🗺️",
			category: "AI & Analytics"
		}
	];

	// Integration Features - Connectivity and interoperability
	const integrationFeatures = [
		{
			title: "LMS Integration",
			description: "Seamless integration with popular Learning Management Systems including Moodle, Canvas, and Blackboard.",
			icon: "🔗",
			category: "Integration"
		},
		{
			title: "API Access",
			description: "Comprehensive RESTful APIs for custom integrations and third-party application connectivity.",
			icon: "⚡",
			category: "Integration"
		},
		{
			title: "Data Import/Export",
			description: "Flexible data migration tools supporting multiple formats including CSV, Excel, and JSON.",
			icon: "📤",
			category: "Integration"
		},
		{
			title: "Single Sign-On (SSO)",
			description: "Enterprise-grade SSO integration with SAML, OAuth, and Active Directory for seamless user access.",
			icon: "🔐",
			category: "Integration"
		},
		{
			title: "Third-Party Platforms",
			description: "Integration with employer platforms, government systems, and NGO databases for broader ecosystem connectivity.",
			icon: "🌐",
			category: "Integration"
		},
		{
			title: "Mobile Applications",
			description: "Native mobile apps for iOS and Android with offline capability and real-time synchronization.",
			icon: "📱",
			category: "Integration"
		}
	];

	// Security Features - Data protection and compliance
	const securityFeatures = [
		{
			title: "Blockchain Verification",
			description: "Immutable record keeping using blockchain technology to ensure credential authenticity and prevent tampering.",
			icon: "🔒",
			category: "Security"
		},
		{
			title: "ODPC Compliance",
			description: "Full compliance with Kenya's Office of the Data Protection Commissioner guidelines and international standards.",
			icon: "✅",
			category: "Security"
		},
		{
			title: "Role-Based Access Control",
			description: "Granular permission system ensuring users only access data appropriate to their role and responsibilities.",
			icon: "👤",
			category: "Security"
		},
		{
			title: "Data Encryption",
			description: "End-to-end encryption for data in transit and at rest using industry-standard AES-256 encryption.",
			icon: "🛡️",
			category: "Security"
		},
		{
			title: "Audit Trails",
			description: "Comprehensive logging of all system activities with tamper-proof audit trails for compliance and security.",
			icon: "📝",
			category: "Security"
		},
		{
			title: "Privacy Controls",
			description: "Advanced privacy settings allowing students and institutions to control data sharing and visibility.",
			icon: "🔏",
			category: "Security"
		}
	];

	// Analytics Features - Insights and reporting capabilities
	const analyticsFeatures = [
		{
			title: "Real-Time Dashboards",
			description: "Live data visualization with customizable widgets and interactive charts for immediate insights.",
			icon: "📊",
			category: "Analytics"
		},
		{
			title: "Custom Reports",
			description: "Flexible report builder allowing institutions to create tailored reports for specific stakeholder needs.",
			icon: "📈",
			category: "Analytics"
		},
		{
			title: "Trend Analysis",
			description: "Historical data analysis revealing long-term trends in student performance and institutional effectiveness.",
			icon: "📉",
			category: "Analytics"
		},
		{
			title: "Comparative Analytics",
			description: "Benchmarking tools for comparing performance across institutions, regions, and demographic groups.",
			icon: "⚖️",
			category: "Analytics"
		},
		{
			title: "Export Capabilities",
			description: "Multiple export formats including PDF, Excel, and PowerPoint for seamless sharing and presentation.",
			icon: "💾",
			category: "Analytics"
		},
		{
			title: "Automated Alerts",
			description: "Intelligent notification system for critical events, milestones, and performance thresholds.",
			icon: "🔔",
			category: "Analytics"
		}
	];
</script>

<div class="page-spacing">
	<!-- Hero Section with Animated Background -->
	<section class="section-spacing">
		<SectionHeader
			sectionName="Features"
			headline="Comprehensive platform for modern education"
			subheadline="<p>Discover the powerful features that make Evoprof the leading choice for educational institutions, government bodies, and employers across Kenya and beyond.</p>"
			color="#3B82F6"
		/>
	</section>

	<!-- Core Features Section (No Animated Background) -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<div class="text-center space-y-4">
			<div class="subsection-subitem-text py-0.1 flex h-fit w-fit items-center justify-center rounded-full border px-2 font-[400] tracking-tighter mx-auto"
				 style="color: #10B981; background-color: color-mix(in srgb, #10B981 20%, transparent);">
				Core Platform
			</div>
			<h2 class="section-headline-text">Essential tools for skill tracking</h2>
			<p class="section-subheadline-text">The foundational features that power student skill development and institutional oversight.</p>
		</div>

		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full max-w-6xl">
			{#each coreFeatures as feature (feature.title)}
				<Card.Root class="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
					<Card.Content class="p-6">
						<div class="flex flex-col items-start gap-4">
							<div class="flex items-center gap-3">
								<div class="text-3xl">{feature.icon}</div>
								<Badge variant="secondary" class="bg-emerald-100 text-emerald-700 dark:bg-emerald-900 dark:text-emerald-300">
									{feature.category}
								</Badge>
							</div>
							<div class="space-y-2">
								<Card.Title class="text-lg font-semibold group-hover:text-primary transition-colors">
									{feature.title}
								</Card.Title>
								<Card.Description class="text-muted-foreground leading-relaxed">
									{feature.description}
								</Card.Description>
							</div>
						</div>
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	</section>

	<!-- Advanced AI Features Section with Animated Background -->
	<section class="section-spacing">
		<SectionHeader
			sectionName="AI & Analytics"
			headline="Intelligent insights for better outcomes"
			subheadline="<p>Advanced artificial intelligence and machine learning capabilities that transform raw data into actionable insights for improved educational outcomes.</p>"
			color="#8B5CF6"
		/>
	</section>

	<!-- Advanced Features Grid (No Animated Background) -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full max-w-6xl">
			{#each advancedFeatures as feature (feature.title)}
				<Card.Root class="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
					<Card.Content class="p-6">
						<div class="flex flex-col items-start gap-4">
							<div class="flex items-center gap-3">
								<div class="text-3xl">{feature.icon}</div>
								<Badge variant="secondary" class="bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300">
									{feature.category}
								</Badge>
							</div>
							<div class="space-y-2">
								<Card.Title class="text-lg font-semibold group-hover:text-primary transition-colors">
									{feature.title}
								</Card.Title>
								<Card.Description class="text-muted-foreground leading-relaxed">
									{feature.description}
								</Card.Description>
							</div>
						</div>
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	</section>

	<!-- Integration Features Section (No Animated Background) -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<div class="text-center space-y-4">
			<div class="subsection-subitem-text py-0.1 flex h-fit w-fit items-center justify-center rounded-full border px-2 font-[400] tracking-tighter mx-auto"
				 style="color: #F59E0B; background-color: color-mix(in srgb, #F59E0B 20%, transparent);">
				Integration
			</div>
			<h2 class="section-headline-text">Seamless connectivity</h2>
			<p class="section-subheadline-text">Connect Evoprof with your existing systems and workflows for a unified educational ecosystem.</p>
		</div>

		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full max-w-6xl">
			{#each integrationFeatures as feature (feature.title)}
				<Card.Root class="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
					<Card.Content class="p-6">
						<div class="flex flex-col items-start gap-4">
							<div class="flex items-center gap-3">
								<div class="text-3xl">{feature.icon}</div>
								<Badge variant="secondary" class="bg-amber-100 text-amber-700 dark:bg-amber-900 dark:text-amber-300">
									{feature.category}
								</Badge>
							</div>
							<div class="space-y-2">
								<Card.Title class="text-lg font-semibold group-hover:text-primary transition-colors">
									{feature.title}
								</Card.Title>
								<Card.Description class="text-muted-foreground leading-relaxed">
									{feature.description}
								</Card.Description>
							</div>
						</div>
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	</section>

	<!-- Security & Compliance Section with Animated Background -->
	<section class="section-spacing">
		<SectionHeader
			sectionName="Security & Compliance"
			headline="Enterprise-grade security you can trust"
			subheadline="<p>Built with security-first principles and full compliance with local and international data protection standards to keep your data safe and secure.</p>"
			color="#EF4444"
		/>
	</section>

	<!-- Security Features Grid (No Animated Background) -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full max-w-6xl">
			{#each securityFeatures as feature (feature.title)}
				<Card.Root class="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
					<Card.Content class="p-6">
						<div class="flex flex-col items-start gap-4">
							<div class="flex items-center gap-3">
								<div class="text-3xl">{feature.icon}</div>
								<Badge variant="secondary" class="bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300">
									{feature.category}
								</Badge>
							</div>
							<div class="space-y-2">
								<Card.Title class="text-lg font-semibold group-hover:text-primary transition-colors">
									{feature.title}
								</Card.Title>
								<Card.Description class="text-muted-foreground leading-relaxed">
									{feature.description}
								</Card.Description>
							</div>
						</div>
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	</section>

	<!-- Analytics & Reporting Section (No Animated Background) -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<div class="text-center space-y-4">
			<div class="subsection-subitem-text py-0.1 flex h-fit w-fit items-center justify-center rounded-full border px-2 font-[400] tracking-tighter mx-auto"
				 style="color: #06B6D4; background-color: color-mix(in srgb, #06B6D4 20%, transparent);">
				Analytics & Reporting
			</div>
			<h2 class="section-headline-text">Data-driven decision making</h2>
			<p class="section-subheadline-text">Comprehensive analytics and reporting tools that transform data into actionable insights for all stakeholders.</p>
		</div>

		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full max-w-6xl">
			{#each analyticsFeatures as feature (feature.title)}
				<Card.Root class="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
					<Card.Content class="p-6">
						<div class="flex flex-col items-start gap-4">
							<div class="flex items-center gap-3">
								<div class="text-3xl">{feature.icon}</div>
								<Badge variant="secondary" class="bg-cyan-100 text-cyan-700 dark:bg-cyan-900 dark:text-cyan-300">
									{feature.category}
								</Badge>
							</div>
							<div class="space-y-2">
								<Card.Title class="text-lg font-semibold group-hover:text-primary transition-colors">
									{feature.title}
								</Card.Title>
								<Card.Description class="text-muted-foreground leading-relaxed">
									{feature.description}
								</Card.Description>
							</div>
						</div>
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	</section>

	<!-- Call-to-Action Section with Animated Background -->
	<section class="section-spacing">
		<SectionHeader
			sectionName="Get Started"
			headline="Ready to transform your institution?"
			subheadline="<p>Experience the power of Evoprof's comprehensive feature set. Join leading educational institutions across Kenya in revolutionizing skill tracking and development.</p>"
			color="#10B981"
		/>
	</section>

	<!-- CTA Content (No Animated Background) -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<div class="flex flex-col items-center gap-8 text-center max-w-4xl">
			<!-- Feature Highlights -->
			<div class="grid grid-cols-1 md:grid-cols-3 gap-6 w-full">
				<div class="space-y-3">
					<div class="text-4xl">🚀</div>
					<h3 class="text-lg font-semibold">Quick Implementation</h3>
					<p class="text-sm text-muted-foreground">Get your institution up and running with Evoprof in just 24-48 hours with our guided onboarding process.</p>
				</div>
				<div class="space-y-3">
					<div class="text-4xl">🎯</div>
					<h3 class="text-lg font-semibold">Proven Results</h3>
					<p class="text-sm text-muted-foreground">Join 156+ institutions already seeing 89% improvement in student tracking efficiency and outcomes.</p>
				</div>
				<div class="space-y-3">
					<div class="text-4xl">🤝</div>
					<h3 class="text-lg font-semibold">Expert Support</h3>
					<p class="text-sm text-muted-foreground">Dedicated account managers and technical support ensure your success every step of the way.</p>
				</div>
			</div>

			<!-- Action Buttons -->
			<div class="flex flex-col sm:flex-row gap-4 w-full sm:w-auto">
				<Button size="lg" href="/contact-sales" class="text-lg px-8 py-3">
					Schedule a Demo
				</Button>
				<Button variant="outline" size="lg" href="/pricing" class="text-lg px-8 py-3">
					View Pricing Plans
				</Button>
			</div>

			<!-- Trust Indicators -->
			<div class="grid grid-cols-2 md:grid-cols-4 gap-4 w-full max-w-2xl text-center">
				<div class="space-y-1">
					<div class="text-2xl font-bold text-primary">12,847+</div>
					<div class="text-xs text-muted-foreground">Students Tracked</div>
				</div>
				<div class="space-y-1">
					<div class="text-2xl font-bold text-primary">156+</div>
					<div class="text-xs text-muted-foreground">Partner Institutions</div>
				</div>
				<div class="space-y-1">
					<div class="text-2xl font-bold text-primary">89.2%</div>
					<div class="text-xs text-muted-foreground">Completion Rate</div>
				</div>
				<div class="space-y-1">
					<div class="text-2xl font-bold text-primary">3,421+</div>
					<div class="text-xs text-muted-foreground">Skills Certified</div>
				</div>
			</div>

			<!-- Additional Information -->
			<div class="space-y-4 max-w-2xl">
				<p class="page-text">
					Evoprof's comprehensive feature set is designed to grow with your institution. From small schools to large university systems, our platform scales to meet your needs while maintaining the highest standards of security and compliance.
				</p>
				<div class="flex flex-wrap justify-center gap-2">
					<Badge variant="outline" class="text-xs">ODPC Compliant</Badge>
					<Badge variant="outline" class="text-xs">Blockchain Secured</Badge>
					<Badge variant="outline" class="text-xs">AI-Powered</Badge>
					<Badge variant="outline" class="text-xs">24/7 Support</Badge>
					<Badge variant="outline" class="text-xs">Mobile Ready</Badge>
				</div>
			</div>
		</div>
	</section>
</div>
