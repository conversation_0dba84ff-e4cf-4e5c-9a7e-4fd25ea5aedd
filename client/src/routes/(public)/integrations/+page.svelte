<script lang="ts">
	/**
	 * Integrations Page Component
	 *
	 * Comprehensive showcase of Evoprof's integration capabilities organized into logical categories:
	 * - Learning Management Systems (LMS) for seamless course and content integration
	 * - Student Information Systems (SIS) for comprehensive student data management
	 * - Educational tools and platforms for enhanced learning experiences
	 * - Government and regulatory systems for compliance and reporting
	 * - Employer and talent platforms for career pathway connections
	 * - Developer tools and APIs for custom integrations and extensions
	 *
	 * Design follows established patterns with alternating animated backgrounds
	 * between sections to maintain visual interest and consistency with the features page.
	 */

	import { But<PERSON> } from "$lib/components/ui/button";
	import SectionHeader from "$lib/components/ui/section-header/SectionHeader.svelte";
	import * as Card from "$lib/components/ui/card";
	import { Badge } from "$lib/components/ui/badge";

	// LMS Integrations - Learning Management System connections
	const lmsIntegrations = [
		{
			title: "Moodle Integration",
			description: "Deep integration with Moodle LMS for automatic grade sync, course enrollment, and skill tracking across all course activities.",
			icon: "📚",
			category: "LMS",
			features: ["Grade Sync", "Course Enrollment", "Activity Tracking"]
		},
		{
			title: "Canvas LMS",
			description: "Seamless Canvas integration enabling real-time skill assessment and progress tracking within existing course workflows.",
			icon: "🎨",
			category: "LMS",
			features: ["Real-time Sync", "Assignment Integration", "Gradebook Sync"]
		},
		{
			title: "Blackboard Learn",
			description: "Comprehensive Blackboard integration for institutional-grade skill tracking and competency-based education support.",
			icon: "⚫",
			category: "LMS",
			features: ["Competency Mapping", "Grade Passback", "User Provisioning"]
		},
		{
			title: "Google Classroom",
			description: "Native Google Classroom integration for K-12 and higher education institutions using Google Workspace for Education.",
			icon: "🏫",
			category: "LMS",
			features: ["Assignment Sync", "Roster Import", "Progress Reports"]
		},
		{
			title: "Microsoft Teams Education",
			description: "Integration with Microsoft Teams for Education, enabling skill tracking within collaborative learning environments.",
			icon: "👥",
			category: "LMS",
			features: ["Team Integration", "Assignment Tracking", "Collaboration Metrics"]
		},
		{
			title: "Schoology",
			description: "Schoology platform integration for K-12 institutions with comprehensive gradebook and assessment synchronization.",
			icon: "🏛️",
			category: "LMS",
			features: ["Gradebook Sync", "Assessment Integration", "Parent Portal"]
		}
	];

	// SIS Integrations - Student Information System connections
	const sisIntegrations = [
		{
			title: "PowerSchool SIS",
			description: "Complete PowerSchool integration for student enrollment, demographic data, and academic record synchronization.",
			icon: "⚡",
			category: "SIS",
			features: ["Student Enrollment", "Demographic Sync", "Academic Records"]
		},
		{
			title: "Infinite Campus",
			description: "Infinite Campus SIS integration providing comprehensive student data management and real-time enrollment updates.",
			icon: "🏫",
			category: "SIS",
			features: ["Real-time Updates", "Parent Portal", "Attendance Tracking"]
		},
		{
			title: "Skyward SIS",
			description: "Skyward Student Management Suite integration for complete student lifecycle management and data synchronization.",
			icon: "☁️",
			category: "SIS",
			features: ["Lifecycle Management", "Data Sync", "Reporting Integration"]
		},
		{
			title: "Clever SSO",
			description: "Clever single sign-on integration for seamless user authentication and automatic roster synchronization.",
			icon: "🔐",
			category: "SIS",
			features: ["Single Sign-On", "Roster Sync", "Automated Provisioning"]
		},
		{
			title: "ClassLink",
			description: "ClassLink integration for unified access management and comprehensive student data synchronization across platforms.",
			icon: "🔗",
			category: "SIS",
			features: ["Access Management", "Data Sync", "Multi-platform Support"]
		},
		{
			title: "Ellucian Banner",
			description: "Higher education ERP integration with Ellucian Banner for comprehensive student information management.",
			icon: "🎓",
			category: "SIS",
			features: ["ERP Integration", "Student Records", "Academic Planning"]
		}
	];

	// Educational Tools - Third-party educational platform integrations
	const educationalTools = [
		{
			title: "Khan Academy",
			description: "Khan Academy integration for tracking personalized learning progress and skill mastery across diverse subject areas.",
			icon: "🧠",
			category: "EdTech",
			features: ["Progress Tracking", "Skill Mastery", "Personalized Learning"]
		},
		{
			title: "Coursera for Campus",
			description: "Integration with Coursera for Campus to track professional skill development and certificate completion.",
			icon: "🎯",
			category: "EdTech",
			features: ["Certificate Tracking", "Skill Development", "Professional Courses"]
		},
		{
			title: "edX for Business",
			description: "edX platform integration for tracking massive open online course (MOOC) completion and skill verification.",
			icon: "📖",
			category: "EdTech",
			features: ["MOOC Tracking", "Skill Verification", "Certificate Management"]
		},
		{
			title: "Duolingo for Schools",
			description: "Language learning integration with Duolingo for Schools to track language proficiency and skill development.",
			icon: "🌍",
			category: "EdTech",
			features: ["Language Proficiency", "Progress Tracking", "Skill Assessment"]
		},
		{
			title: "Codecademy",
			description: "Codecademy integration for tracking programming and technical skill development in computer science education.",
			icon: "💻",
			category: "EdTech",
			features: ["Programming Skills", "Technical Assessment", "Project Tracking"]
		},
		{
			title: "LinkedIn Learning",
			description: "LinkedIn Learning integration for professional development tracking and industry-relevant skill certification.",
			icon: "💼",
			category: "EdTech",
			features: ["Professional Development", "Industry Skills", "Certification Tracking"]
		}
	];

	// Employer & Talent Platforms - Career pathway connections
	const employerIntegrations = [
		{
			title: "LinkedIn Talent Solutions",
			description: "Integration with LinkedIn for talent pipeline management and skills-based recruitment from verified student profiles.",
			icon: "💼",
			category: "Talent",
			features: ["Talent Pipeline", "Skills Verification", "Recruitment Integration"]
		},
		{
			title: "Indeed for Employers",
			description: "Indeed platform integration for job matching based on verified skills and competencies tracked through Evoprof.",
			icon: "🔍",
			category: "Talent",
			features: ["Job Matching", "Skills-based Hiring", "Candidate Verification"]
		},
		{
			title: "BrighterMonday Kenya",
			description: "Local job platform integration for connecting Kenyan students with employers based on verified skill profiles.",
			icon: "🌅",
			category: "Talent",
			features: ["Local Job Market", "Skill Matching", "Career Guidance"]
		},
		{
			title: "Andela Talent Network",
			description: "Integration with Andela's talent network for technology skill verification and career pathway development.",
			icon: "🚀",
			category: "Talent",
			features: ["Tech Skills", "Career Pathways", "Talent Network"]
		},
		{
			title: "Corporate Training Platforms",
			description: "Integration with corporate learning platforms for seamless transition from education to workplace training.",
			icon: "🏢",
			category: "Talent",
			features: ["Corporate Training", "Skill Transition", "Workplace Integration"]
		},
		{
			title: "Freelance Platforms",
			description: "Integration with freelance platforms like Upwork and Fiverr for skills-based project matching and verification.",
			icon: "💻",
			category: "Talent",
			features: ["Project Matching", "Freelance Skills", "Portfolio Integration"]
		}
	];

	// Developer Tools & APIs - Technical integration capabilities
	const developerTools = [
		{
			title: "RESTful API",
			description: "Comprehensive REST API with full CRUD operations, authentication, and real-time data access for custom integrations.",
			icon: "🔌",
			category: "API",
			features: ["REST Endpoints", "Authentication", "Real-time Data"]
		},
		{
			title: "GraphQL API",
			description: "Flexible GraphQL API for efficient data querying and real-time subscriptions with customizable data structures.",
			icon: "📊",
			category: "API",
			features: ["GraphQL Queries", "Subscriptions", "Custom Schemas"]
		},
		{
			title: "Webhooks",
			description: "Real-time webhook notifications for skill updates, milestone achievements, and system events.",
			icon: "🔔",
			category: "API",
			features: ["Real-time Notifications", "Event Triggers", "Custom Endpoints"]
		},
		{
			title: "SDK Libraries",
			description: "Official SDKs for popular programming languages including Python, JavaScript, Java, and PHP.",
			icon: "📚",
			category: "API",
			features: ["Multi-language SDKs", "Code Examples", "Documentation"]
		},
		{
			title: "Data Export Tools",
			description: "Automated data export capabilities with support for CSV, JSON, XML, and custom formats.",
			icon: "📤",
			category: "API",
			features: ["Multiple Formats", "Scheduled Exports", "Custom Fields"]
		},
		{
			title: "Integration Marketplace",
			description: "Marketplace of pre-built integrations and connectors developed by our partner ecosystem.",
			icon: "🛒",
			category: "API",
			features: ["Pre-built Connectors", "Partner Ecosystem", "Easy Installation"]
		}
	];

	// Government & Regulatory - Official system integrations
	const governmentIntegrations = [
		{
			title: "Kenya Education Management Information System (KEMIS)",
			description: "Official integration with KEMIS for national education data reporting and compliance with government standards.",
			icon: "🏛️",
			category: "Government",
			features: ["National Reporting", "Compliance", "Data Standards"]
		},
		{
			title: "TVET Authority Systems",
			description: "Integration with Technical and Vocational Education and Training Authority systems for skills certification.",
			icon: "🔧",
			category: "Government",
			features: ["Skills Certification", "TVET Compliance", "Authority Reporting"]
		},
		{
			title: "Commission for University Education (CUE)",
			description: "CUE system integration for higher education quality assurance and academic standard compliance.",
			icon: "🎓",
			category: "Government",
			features: ["Quality Assurance", "Academic Standards", "University Compliance"]
		},
		{
			title: "Kenya National Qualifications Authority (KNQA)",
			description: "KNQA integration for national qualifications framework alignment and skills recognition.",
			icon: "📜",
			category: "Government",
			features: ["Qualifications Framework", "Skills Recognition", "National Standards"]
		},
		{
			title: "Ministry of Education Data Systems",
			description: "Direct integration with Ministry of Education databases for policy compliance and national education planning.",
			icon: "🏢",
			category: "Government",
			features: ["Policy Compliance", "National Planning", "Education Statistics"]
		},
		{
			title: "County Government Education Departments",
			description: "County-level education department integration for local education management and resource allocation.",
			icon: "🗺️",
			category: "Government",
			features: ["Local Management", "Resource Allocation", "County Reporting"]
		}
	];
</script>

<div class="page-spacing">
	<!-- Hero Section with Animated Background -->
	<section class="section-spacing">
		<SectionHeader
			sectionName="Integrations"
			headline="Connect with your existing educational ecosystem"
			subheadline="<p>Evoprof seamlessly integrates with the tools and systems you already use, creating a unified educational technology ecosystem that enhances rather than replaces your current workflows.</p>"
			color="#3B82F6"
		/>
	</section>

	<!-- LMS Integrations Section (No Animated Background) -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<div class="text-center space-y-4">
			<div class="subsection-subitem-text py-0.1 flex h-fit w-fit items-center justify-center rounded-full border px-2 font-[400] tracking-tighter mx-auto"
				 style="color: #10B981; background-color: color-mix(in srgb, #10B981 20%, transparent);">
				Learning Management Systems
			</div>
			<h2 class="section-headline-text">Seamless LMS integration</h2>
			<p class="section-subheadline-text">Connect Evoprof with your existing Learning Management System for unified course management and skill tracking.</p>
		</div>

		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full max-w-6xl">
			{#each lmsIntegrations as integration (integration.title)}
				<Card.Root class="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
					<Card.Content class="p-6">
						<div class="flex flex-col items-start gap-4">
							<div class="flex items-center gap-3">
								<div class="text-3xl">{integration.icon}</div>
								<Badge variant="secondary" class="bg-emerald-100 text-emerald-700 dark:bg-emerald-900 dark:text-emerald-300">
									{integration.category}
								</Badge>
							</div>
							<div class="space-y-3">
								<Card.Title class="text-lg font-semibold group-hover:text-primary transition-colors">
									{integration.title}
								</Card.Title>
								<Card.Description class="text-muted-foreground leading-relaxed">
									{integration.description}
								</Card.Description>
								<div class="flex flex-wrap gap-1">
									{#each integration.features as feature}
										<Badge variant="outline" class="text-xs">
											{feature}
										</Badge>
									{/each}
								</div>
							</div>
						</div>
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	</section>

	<!-- SIS & Academic Systems Section with Animated Background -->
	<section class="section-spacing">
		<SectionHeader
			sectionName="Student Information Systems"
			headline="Comprehensive student data integration"
			subheadline="<p>Synchronize student information, enrollment data, and academic records across your institution's core systems for complete data consistency and streamlined administration.</p>"
			color="#8B5CF6"
		/>
	</section>

	<!-- SIS Integrations Grid (No Animated Background) -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full max-w-6xl">
			{#each sisIntegrations as integration (integration.title)}
				<Card.Root class="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
					<Card.Content class="p-6">
						<div class="flex flex-col items-start gap-4">
							<div class="flex items-center gap-3">
								<div class="text-3xl">{integration.icon}</div>
								<Badge variant="secondary" class="bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300">
									{integration.category}
								</Badge>
							</div>
							<div class="space-y-3">
								<Card.Title class="text-lg font-semibold group-hover:text-primary transition-colors">
									{integration.title}
								</Card.Title>
								<Card.Description class="text-muted-foreground leading-relaxed">
									{integration.description}
								</Card.Description>
								<div class="flex flex-wrap gap-1">
									{#each integration.features as feature}
										<Badge variant="outline" class="text-xs">
											{feature}
										</Badge>
									{/each}
								</div>
							</div>
						</div>
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	</section>

	<!-- Educational Tools Section (No Animated Background) -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<div class="text-center space-y-4">
			<div class="subsection-subitem-text py-0.1 flex h-fit w-fit items-center justify-center rounded-full border px-2 font-[400] tracking-tighter mx-auto"
				 style="color: #F59E0B; background-color: color-mix(in srgb, #F59E0B 20%, transparent);">
				Educational Tools
			</div>
			<h2 class="section-headline-text">Enhanced learning experiences</h2>
			<p class="section-subheadline-text">Integrate with popular educational platforms and tools to track skill development across diverse learning environments.</p>
		</div>

		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full max-w-6xl">
			{#each educationalTools as integration (integration.title)}
				<Card.Root class="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
					<Card.Content class="p-6">
						<div class="flex flex-col items-start gap-4">
							<div class="flex items-center gap-3">
								<div class="text-3xl">{integration.icon}</div>
								<Badge variant="secondary" class="bg-amber-100 text-amber-700 dark:bg-amber-900 dark:text-amber-300">
									{integration.category}
								</Badge>
							</div>
							<div class="space-y-3">
								<Card.Title class="text-lg font-semibold group-hover:text-primary transition-colors">
									{integration.title}
								</Card.Title>
								<Card.Description class="text-muted-foreground leading-relaxed">
									{integration.description}
								</Card.Description>
								<div class="flex flex-wrap gap-1">
									{#each integration.features as feature}
										<Badge variant="outline" class="text-xs">
											{feature}
										</Badge>
									{/each}
								</div>
							</div>
						</div>
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	</section>

	<!-- Government & Regulatory Section with Animated Background -->
	<section class="section-spacing">
		<SectionHeader
			sectionName="Government & Regulatory"
			headline="Official compliance and reporting"
			subheadline="<p>Maintain compliance with national education standards and streamline reporting to government agencies and regulatory bodies across Kenya.</p>"
			color="#EF4444"
		/>
	</section>

	<!-- Government Integrations Grid (No Animated Background) -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full max-w-6xl">
			{#each governmentIntegrations as integration (integration.title)}
				<Card.Root class="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
					<Card.Content class="p-6">
						<div class="flex flex-col items-start gap-4">
							<div class="flex items-center gap-3">
								<div class="text-3xl">{integration.icon}</div>
								<Badge variant="secondary" class="bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300">
									{integration.category}
								</Badge>
							</div>
							<div class="space-y-3">
								<Card.Title class="text-lg font-semibold group-hover:text-primary transition-colors">
									{integration.title}
								</Card.Title>
								<Card.Description class="text-muted-foreground leading-relaxed">
									{integration.description}
								</Card.Description>
								<div class="flex flex-wrap gap-1">
									{#each integration.features as feature}
										<Badge variant="outline" class="text-xs">
											{feature}
										</Badge>
									{/each}
								</div>
							</div>
						</div>
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	</section>

	<!-- Employer & Talent Platforms Section (No Animated Background) -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<div class="text-center space-y-4">
			<div class="subsection-subitem-text py-0.1 flex h-fit w-fit items-center justify-center rounded-full border px-2 font-[400] tracking-tighter mx-auto"
				 style="color: #06B6D4; background-color: color-mix(in srgb, #06B6D4 20%, transparent);">
				Employer & Talent Platforms
			</div>
			<h2 class="section-headline-text">Career pathway connections</h2>
			<p class="section-subheadline-text">Bridge the gap between education and employment with direct connections to talent platforms and employer networks.</p>
		</div>

		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full max-w-6xl">
			{#each employerIntegrations as integration (integration.title)}
				<Card.Root class="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
					<Card.Content class="p-6">
						<div class="flex flex-col items-start gap-4">
							<div class="flex items-center gap-3">
								<div class="text-3xl">{integration.icon}</div>
								<Badge variant="secondary" class="bg-cyan-100 text-cyan-700 dark:bg-cyan-900 dark:text-cyan-300">
									{integration.category}
								</Badge>
							</div>
							<div class="space-y-3">
								<Card.Title class="text-lg font-semibold group-hover:text-primary transition-colors">
									{integration.title}
								</Card.Title>
								<Card.Description class="text-muted-foreground leading-relaxed">
									{integration.description}
								</Card.Description>
								<div class="flex flex-wrap gap-1">
									{#each integration.features as feature}
										<Badge variant="outline" class="text-xs">
											{feature}
										</Badge>
									{/each}
								</div>
							</div>
						</div>
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	</section>

	<!-- Developer Tools & APIs Section with Animated Background -->
	<section class="section-spacing">
		<SectionHeader
			sectionName="Developer Tools & APIs"
			headline="Build custom integrations"
			subheadline="<p>Comprehensive developer tools and APIs enable custom integrations, data access, and platform extensions to meet your unique institutional needs.</p>"
			color="#6366F1"
		/>
	</section>

	<!-- Developer Tools Grid (No Animated Background) -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full max-w-6xl">
			{#each developerTools as integration (integration.title)}
				<Card.Root class="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
					<Card.Content class="p-6">
						<div class="flex flex-col items-start gap-4">
							<div class="flex items-center gap-3">
								<div class="text-3xl">{integration.icon}</div>
								<Badge variant="secondary" class="bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300">
									{integration.category}
								</Badge>
							</div>
							<div class="space-y-3">
								<Card.Title class="text-lg font-semibold group-hover:text-primary transition-colors">
									{integration.title}
								</Card.Title>
								<Card.Description class="text-muted-foreground leading-relaxed">
									{integration.description}
								</Card.Description>
								<div class="flex flex-wrap gap-1">
									{#each integration.features as feature}
										<Badge variant="outline" class="text-xs">
											{feature}
										</Badge>
									{/each}
								</div>
							</div>
						</div>
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	</section>

	<!-- Call-to-Action Section with Animated Background -->
	<section class="section-spacing">
		<SectionHeader
			sectionName="Get Connected"
			headline="Ready to integrate with your ecosystem?"
			subheadline="<p>Start connecting Evoprof with your existing systems today. Our integration specialists will help you create a seamless educational technology ecosystem.</p>"
			color="#10B981"
		/>
	</section>

	<!-- CTA Content (No Animated Background) -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<div class="flex flex-col items-center gap-8 text-center max-w-4xl">
			<!-- Integration Highlights -->
			<div class="grid grid-cols-1 md:grid-cols-3 gap-6 w-full">
				<div class="space-y-3">
					<div class="text-4xl">⚡</div>
					<h3 class="text-lg font-semibold">Quick Setup</h3>
					<p class="text-sm text-muted-foreground">Most integrations can be configured in under 30 minutes with our guided setup process and pre-built connectors.</p>
				</div>
				<div class="space-y-3">
					<div class="text-4xl">🔧</div>
					<h3 class="text-lg font-semibold">Custom Solutions</h3>
					<p class="text-sm text-muted-foreground">Need a custom integration? Our development team can build bespoke solutions for your specific requirements.</p>
				</div>
				<div class="space-y-3">
					<div class="text-4xl">🛡️</div>
					<h3 class="text-lg font-semibold">Secure & Reliable</h3>
					<p class="text-sm text-muted-foreground">All integrations use enterprise-grade security with encrypted data transfer and compliance monitoring.</p>
				</div>
			</div>

			<!-- Action Buttons -->
			<div class="flex flex-col sm:flex-row gap-4 w-full sm:w-auto">
				<Button size="lg" href="/contact-sales" class="text-lg px-8 py-3">
					Discuss Integrations
				</Button>
				<Button variant="outline" size="lg" href="/api-docs" class="text-lg px-8 py-3">
					View API Documentation
				</Button>
			</div>

			<!-- Integration Stats -->
			<div class="grid grid-cols-2 md:grid-cols-4 gap-4 w-full max-w-2xl text-center">
				<div class="space-y-1">
					<div class="text-2xl font-bold text-primary">50+</div>
					<div class="text-xs text-muted-foreground">Available Integrations</div>
				</div>
				<div class="space-y-1">
					<div class="text-2xl font-bold text-primary">99.9%</div>
					<div class="text-xs text-muted-foreground">Integration Uptime</div>
				</div>
				<div class="space-y-1">
					<div class="text-2xl font-bold text-primary">24/7</div>
					<div class="text-xs text-muted-foreground">API Monitoring</div>
				</div>
				<div class="space-y-1">
					<div class="text-2xl font-bold text-primary">&lt; 30min</div>
					<div class="text-xs text-muted-foreground">Average Setup Time</div>
				</div>
			</div>

			<!-- Additional Information -->
			<div class="space-y-4 max-w-2xl">
				<p class="page-text">
					Our integration ecosystem is constantly growing. Don't see the integration you need? Contact our team to discuss custom development or request priority support for new platform connections.
				</p>
				<div class="flex flex-wrap justify-center gap-2">
					<Badge variant="outline" class="text-xs">REST API</Badge>
					<Badge variant="outline" class="text-xs">GraphQL</Badge>
					<Badge variant="outline" class="text-xs">Webhooks</Badge>
					<Badge variant="outline" class="text-xs">Real-time Sync</Badge>
					<Badge variant="outline" class="text-xs">Enterprise Security</Badge>
					<Badge variant="outline" class="text-xs">24/7 Support</Badge>
				</div>
			</div>
		</div>
	</section>
</div>
