<script lang="ts">
	import UpdateStarsIcon from "$lib/components/icons/ui/UpdateStarsIcon.svelte";
	import { But<PERSON> } from "$lib/components/ui/button";
	import SectionHeader from "$lib/components/ui/section-header/SectionHeader.svelte";
	import * as Accordion from "$lib/components/ui/accordion";
	import * as Card from "$lib/components/ui/card";
	import { Badge } from "$lib/components/ui/badge";
	import { faqs } from "$config";

	// Features data
	const features = [
		{
			title: "AI-Powered Skill Assessment",
			description: "Advanced algorithms analyze student performance and provide personalized skill development recommendations.",
			icon: "🤖"
		},
		{
			title: "Blockchain Security",
			description: "Tamper-proof records ensure data integrity and provide verifiable credentials for life.",
			icon: "🔒"
		},
		{
			title: "Real-time Analytics",
			description: "Comprehensive dashboards provide instant insights into student progress and institutional performance.",
			icon: "📊"
		},
		{
			title: "Multi-stakeholder Access",
			description: "Tailored interfaces for schools, educators, government bodies, and employers with role-based permissions.",
			icon: "👥"
		},
		{
			title: "Seamless Integration",
			description: "Easy integration with existing educational systems and third-party platforms.",
			icon: "🔗"
		},
		{
			title: "Compliance Ready",
			description: "Built-in compliance with Kenya's ODPC guidelines and international data protection standards.",
			icon: "✅"
		}
	];

	// Benefits data
	const benefits = [
		{
			title: "For Educational Institutions",
			description: "Streamline student tracking, improve outcomes, and demonstrate institutional excellence.",
			stats: "89% improvement in student tracking efficiency"
		},
		{
			title: "For Government Bodies",
			description: "Access comprehensive educational data for policy making and resource allocation.",
			stats: "156 institutions already onboarded"
		},
		{
			title: "For Employers",
			description: "Find qualified candidates with verified skills and authentic credentials.",
			stats: "3,421 skills certified and verified"
		}
	];

	// Testimonials data
	const testimonials = [
		{
			quote: "Evoprof has transformed how we track and develop our students' skills. The AI recommendations are incredibly accurate.",
			author: "Dr. Sarah Kimani",
			role: "Dean of Academic Affairs",
			institution: "Nairobi Technical Institute"
		},
		{
			quote: "The blockchain verification gives us confidence in the authenticity of student credentials. It's a game-changer for recruitment.",
			author: "Michael Ochieng",
			role: "HR Director",
			institution: "TechCorp Kenya"
		},
		{
			quote: "Having real-time insights into educational outcomes across the region helps us make better policy decisions.",
			author: "Prof. Grace Wanjiku",
			role: "Education Policy Advisor",
			institution: "Ministry of Education"
		}
	];

	// Stats data
	const stats = [
		{ number: "12,847", label: "Students Tracked", suffix: "+" },
		{ number: "156", label: "Partner Institutions", suffix: "+" },
		{ number: "89.2", label: "Completion Rate", suffix: "%" },
		{ number: "3,421", label: "Skills Certified", suffix: "+" }
	];
</script>

<div class="page-spacing">
	<section class="section-spacing">
		<!-- Hero Section -->
		<div class="lg:px-18 flex w-full flex-col justify-center gap-4 py-6 md:px-8">
			<div class="flex flex-col gap-4">
				<a
					class="md:text-smlg subsection-subitem-text flex h-fit w-fit items-center justify-start gap-1 rounded-lg border px-2 py-1 md:gap-2"
					href="/changelog"
				>
					<UpdateStarsIcon class="md:size-5.5 size-5" />
					<h6 class="tracking-tight">Version 1.0 is live</h6>
				</a>
				<div class="mx-auto w-full">
					<div class="flex flex-col justify-between gap-8 md:flex-row md:items-center">
						<h1 class="headline-text md:max-w-full">
							Smart skill tracking for future-ready institutions.
						</h1>
						<div class="flex flex-col gap-5 md:max-w-[35%]">
							<p class="page-text">
								Evoprof is your trusted partner for managing student skill profiles, institutional
								reporting, and secure data sharing — powered by AI and blockchain.
							</p>
							<div class="flex gap-4">
								<Button href="/contact-sales">Contact Sales</Button>
								<Button variant="outline" href="/pricing">Explore Plans</Button>
							</div>
						</div>
					</div>
				</div>
			</div>


		</div>
	</section>

	<!-- Features Section -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<div class="text-center space-y-4">
			<div class="subsection-subitem-text py-0.1 flex h-fit w-fit items-center justify-center rounded-full border px-2 font-[400] tracking-tighter mx-auto"
				 style="color: #3B82F6; background-color: color-mix(in srgb, #3B82F6 20%, transparent);">
				Features
			</div>
			<h2 class="section-headline-text">Powerful tools for modern education</h2>
			<p class="section-subheadline-text">Discover the comprehensive features that make Evoprof the leading choice for educational institutions worldwide.</p>
		</div>

		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full max-w-6xl">
			{#each features as feature (feature.title)}
				<Card.Root class="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
					<Card.Content class="p-6">
						<div class="flex flex-col items-start gap-4">
							<div class="text-4xl mb-2">{feature.icon}</div>
							<div class="space-y-2">
								<Card.Title class="text-lg font-semibold group-hover:text-primary transition-colors">
									{feature.title}
								</Card.Title>
								<Card.Description class="text-muted-foreground leading-relaxed">
									{feature.description}
								</Card.Description>
							</div>
						</div>
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	</section>

	<!-- Stats Section -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<SectionHeader
			sectionName="Impact"
			headline="Trusted by institutions across Kenya"
			subheadline="<p>See the real impact Evoprof is making in educational institutions nationwide.</p>"
			color="#10B981"
		/>

		<div class="grid grid-cols-2 md:grid-cols-4 gap-6 w-full max-w-4xl">
			{#each stats as stat (stat.label)}
				<div class="text-center space-y-2">
					<div class="text-3xl md:text-4xl font-bold text-primary">
						{stat.number}{stat.suffix}
					</div>
					<div class="text-sm md:text-base text-muted-foreground font-medium">
						{stat.label}
					</div>
				</div>
			{/each}
		</div>
	</section>

	<!-- Benefits Section -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<div class="text-center space-y-4">
			<div class="subsection-subitem-text py-0.1 flex h-fit w-fit items-center justify-center rounded-full border px-2 font-[400] tracking-tighter mx-auto"
				 style="color: #8B5CF6; background-color: color-mix(in srgb, #8B5CF6 20%, transparent);">
				Benefits
			</div>
			<h2 class="section-headline-text">Value for every stakeholder</h2>
			<p class="section-subheadline-text">Evoprof delivers measurable benefits to all participants in the educational ecosystem.</p>
		</div>

		<div class="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-6xl">
			{#each benefits as benefit (benefit.title)}
				<Card.Root class="text-center hover:shadow-lg transition-all duration-300">
					<Card.Content class="p-6">
						<div class="space-y-4">
							<Card.Title class="text-xl font-semibold">
								{benefit.title}
							</Card.Title>
							<Card.Description class="text-muted-foreground leading-relaxed">
								{benefit.description}
							</Card.Description>
							<Badge variant="secondary" class="bg-primary/10 text-primary">
								{benefit.stats}
							</Badge>
						</div>
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	</section>

	<!-- FAQ Section -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<SectionHeader
			sectionName="FAQ"
			headline="Here are the answers to your questions"
			subheadline="<div class='flex flex-col gap-1'><p>After reading this section, if you still have questions,</p><p>feel free to <a href='/contact' class='text-primary decoration-1 underline'>reach out</a> to us.</p></div>"
			color="#00AD09"
		/>

		<Accordion.Root type="multiple" class="min-w-full md:w-[600px]" variant="noBorderGap">
			{#each faqs as faq (faq.question)}
				<Accordion.Item value={faq.question} class="bg-gray rounded-2xl border">
					<Accordion.Trigger class="cursor-pointer">{faq.question}</Accordion.Trigger>
					<Accordion.Content class="">
						<p class="">{faq.answer}</p>
					</Accordion.Content>
				</Accordion.Item>
			{/each}
		</Accordion.Root>
	</section>

	<!-- Testimonials Section -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<div class="text-center space-y-4">
			<div class="subsection-subitem-text py-0.1 flex h-fit w-fit items-center justify-center rounded-full border px-2 font-[400] tracking-tighter mx-auto"
				 style="color: #F59E0B; background-color: color-mix(in srgb, #F59E0B 20%, transparent);">
				Testimonials
			</div>
			<h2 class="section-headline-text">Trusted by education leaders</h2>
			<p class="section-subheadline-text">Hear from the institutions and professionals who are already transforming education with Evoprof.</p>
		</div>

		<div class="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-6xl">
			{#each testimonials as testimonial (testimonial.author)}
				<Card.Root class="hover:shadow-lg transition-all duration-300">
					<Card.Content class="p-6">
						<div class="space-y-4">
							<div class="text-lg text-muted-foreground italic leading-relaxed">
								"{testimonial.quote}"
							</div>
							<div class="space-y-1">
								<div class="font-semibold text-foreground">
									{testimonial.author}
								</div>
								<div class="text-sm text-muted-foreground">
									{testimonial.role}
								</div>
								<div class="text-sm text-primary font-medium">
									{testimonial.institution}
								</div>
							</div>
						</div>
					</Card.Content>
				</Card.Root>
			{/each}
		</div>
	</section>

	<!-- CTA Section -->
	<section class="section-spacing flex flex-col gap-8 pb-16">
		<SectionHeader
			sectionName="Get Started"
			headline="Ready to transform your institution?"
			subheadline="<p>Join the growing community of educational institutions using Evoprof to track, develop, and verify student skills.</p>"
			color="#EF4444"
		/>

		<div class="flex flex-col items-center gap-6 text-center max-w-2xl">
			<div class="space-y-4">
				<p class="page-text text-lg">
					Start your journey with Evoprof today. Our team will help you onboard your institution and get your students' skill tracking up and running in no time.
				</p>
			</div>

			<div class="flex flex-col sm:flex-row gap-4 w-full sm:w-auto">
				<Button size="lg" href="/contact-sales" class="text-lg px-8 py-3">
					Schedule a Demo
				</Button>
				<Button variant="outline" size="lg" href="/pricing" class="text-lg px-8 py-3">
					View Pricing
				</Button>
			</div>

			<div class="grid grid-cols-1 sm:grid-cols-3 gap-4 w-full max-w-md text-center">
				<div class="space-y-1">
					<div class="text-sm font-semibold text-foreground">Quick Setup</div>
					<div class="text-xs text-muted-foreground">Get started in 24 hours</div>
				</div>
				<div class="space-y-1">
					<div class="text-sm font-semibold text-foreground">Expert Support</div>
					<div class="text-xs text-muted-foreground">Dedicated account manager</div>
				</div>
				<div class="space-y-1">
					<div class="text-sm font-semibold text-foreground">Secure & Compliant</div>
					<div class="text-xs text-muted-foreground">ODPC guidelines compliant</div>
				</div>
			</div>
		</div>
	</section>
</div>
