<script>
	import { <PERSON><PERSON> } from "$lib/components/ui/button";
	import { Input } from "$lib/components/ui/input";
	import { Label } from "$lib/components/ui/label";
	import {
		Card,
		CardContent,
		CardDescription,
		CardHeader,
		CardTitle
	} from "$lib/components/ui/card";
	import { BrandLogoIcon } from "$lib/components/icons/brand";

	let email = $state("");
	let isSubmitted = $state(false);

	function handleResetPassword() {
		console.log("Reset password for:", email);
		isSubmitted = true;
	}

	function handleBackToLogin() {
		window.location.href = "/login";
	}

	function handleResendEmail() {
		console.log("Resend reset email to:", email);
		/**
		 * TODO: Implement resend email call and ui states.
		 */
	}
</script>

<div class="flex h-[calc(100vh-3.25rem)] items-center justify-center p-4">
	<div class="w-full max-w-md">
		<!-- Logo -->
		<div class="mb-8 flex justify-center">
			<div class="bg-primary flex h-16 w-16 items-center justify-center rounded-2xl shadow-sm">
				<BrandLogoIcon class="text-accent p-3" />
			</div>
		</div>

		<!-- Reset Password Card -->
		<Card class="border-0 shadow-lg">
			{#if !isSubmitted}
				<!-- Initial Reset Form -->
				<CardHeader class="space-y-2 text-center">
					<CardTitle class="text-2xl font-semibold">Reset your password</CardTitle>
					<CardDescription class="text-slate-600">
						Enter your email address and we'll send you a link to reset your password.
					</CardDescription>
				</CardHeader>

				<CardContent class="space-y-6">
					<div class="space-y-4">
						<div class="space-y-2">
							<Label for="email">Email address</Label>
							<Input
								id="email"
								type="email"
								placeholder="<EMAIL>"
								class="h-12"
								bind:value={email}
							/>
						</div>

						<Button
							class="h-12 w-full cursor-pointer bg-black hover:bg-slate-800"
							onclick={handleResetPassword}
							disabled={!email}
						>
							Send reset link
						</Button>
					</div>
				</CardContent>
			{:else}
				<!-- Success State -->
				<CardHeader class="space-y-4 text-center">
					<!-- Success Icon -->
					<div class="flex justify-center">
						<div class="flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
							<svg
								class="h-8 w-8 text-green-600"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M5 13l4 4L19 7"
								/>
							</svg>
						</div>
					</div>

					<div class="space-y-2">
						<CardTitle class="text-2xl font-semibold">Check your email</CardTitle>
						<CardDescription class="text-slate-600">
							We've sent a password reset link to <br />
							<span class="font-medium text-slate-900">{email}</span>
						</CardDescription>
					</div>
				</CardHeader>

				<CardContent class="space-y-6">
					<div class="rounded-lg bg-slate-50 p-4 text-center text-sm text-slate-600">
						<p>Didn't receive the email? Check your spam folder or try again.</p>
					</div>

					<div class="space-y-3">
						<Button variant="outline" class="h-12 w-full" onclick={handleResendEmail}>
							Resend email
						</Button>

						<Button class="h-12 w-full bg-black hover:bg-slate-800" onclick={handleBackToLogin}>
							Back to login
						</Button>
					</div>
				</CardContent>
			{/if}
		</Card>

		<!-- Help Text -->
		{#if !isSubmitted}
			<div class="mt-6 text-center">
				<p class="text-sm text-slate-600">
					Remember your password?
					<a class="cursor-pointer font-medium text-slate-900 hover:underline" href="/login">
						Login
					</a>
				</p>
			</div>
		{/if}
	</div>
</div>
