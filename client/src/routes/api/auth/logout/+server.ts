/**
 * Logout API route
 *
 * This API route handles user logout by clearing authentication cookies
 * and optionally invalidating tokens on the backend (if supported).
 */

import { json } from "@sveltejs/kit";
import type { RequestHandler } from "./$types";

/**
 * Handle POST requests for user logout
 *
 * Clears authentication cookies and returns success response.
 * Since JWT tokens are stateless, we primarily need to clear client-side storage.
 */
export const POST: RequestHandler = async ({ cookies }) => {
	try {
		// Clear authentication cookies
		cookies.delete("access_token", { path: "/" });
		cookies.delete("refresh_token", { path: "/" });
		cookies.delete("user_data", { path: "/" });

		// TODO: If the backend supports token blacklisting, make a request here
		// to invalidate the tokens on the server side as well

		return json({
			success: true,
			message: "Logged out successfully"
		});
	} catch (error) {
		console.error("Logout API error:", error);

		return json(
			{
				success: false,
				message: "An error occurred during logout"
			},
			{ status: 500 }
		);
	}
};

/**
 * Handle OPTIONS requests for CORS preflight
 */
export const OPTIONS: RequestHandler = async () => {
	return new Response(null, {
		status: 200,
		headers: {
			"Access-Control-Allow-Origin": "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization"
		}
	});
};
