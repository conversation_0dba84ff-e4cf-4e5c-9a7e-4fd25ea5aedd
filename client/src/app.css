/* Google fonts imports */
@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Roboto+Serif&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Space+Mono:ital,wght@0,400;0,700;1,400;1,700&display=swap");

@import "tailwindcss";

@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme {
	/* Custom font variables */
	--font-sans: "Inter", sans-serif;
	--font-serif: "Roboto Serif", serif;
	--font-mono: "Space Mono", monospace;

	/* Custom breakpoint variables */
	--breakpoint-md: 810px;
	--breakpoint-mdlg: 1000px;
	--breakpoint-lg: 1300px;

	--width-md: 810px;
	--width-mdlg: 1000px;
	--width-lg: 1300px;

	--container-md: 810px;
	--container-mdlg: 1000px;
	--container-lg: 1300px;

	/* Custom text sizes */
	--text-smlg: 0.8rem;
	--text-headline: 2rem;
	--text-headlinemd: 2.4rem;
	--text-headlinemdlg: 3rem;
	--text-headlinelg: 4rem;
	/* Subheadline. Used for all normal text */
	--text-page: 0.9rem;
	--text-pagemd: 1rem;
	--text-pagemdlg: 1.6rem;
	--text-pagelg: 1.8rem;
	/* Section headline. Used for all section headlines */
	--text-sectionheadline: 1.5rem;
	--text-sectionheadlinemd: 2rem;
	--text-sectionheadlinemdlg: 1.6rem;
	--text-sectionheadlinelg: 1.8rem;
	/* Subsection subitems */
	--text-subsectionsubitem: 0.7rem;
	--text-subsectionsubitemmd: 0.8rem;
	--text-subsectionsubitemmdlg: 1.6rem;
	--text-subsectionsubitemlg: 1.8rem;
}

:root {
	--background: oklch(0.9821 0 0);
	--foreground: oklch(0.3211 0 0);
	--card: oklch(1 0 0);
	--card-foreground: oklch(0.3211 0 0);
	--popover: oklch(1 0 0);
	--popover-foreground: oklch(0.3211 0 0);
	--primary: oklch(0.777 0.1226 190.94);
	--primary-foreground: oklch(1 0 0);
	--secondary: oklch(0.8537 0.0862 203.0686);
	--secondary-foreground: oklch(0.3211 0 0);
	--muted: oklch(0.8452 0 0);
	--muted-foreground: oklch(0.5103 0 0);
	--accent: oklch(0.3351 0.0816 243.3375);
	--accent-foreground: oklch(1 0 0);
	--destructive: oklch(0.652 0.234 26.6909);
	--destructive-foreground: oklch(1 0 0);
	--border: oklch(0.8975 0 0);
	--input: oklch(1 0 0);
	--ring: oklch(0.8537 0.0862 203.0686);
	--chart-1: oklch(0.777 0.1226 190.94);
	--chart-2: oklch(0.8537 0.0862 203.0686);
	--chart-3: oklch(0.3351 0.0816 243.3375);
	--chart-4: oklch(0.772 0.1738 64.552);
	--chart-5: oklch(0.652 0.234 26.6909);
	--sidebar: oklch(0.9821 0 0);
	--sidebar-foreground: oklch(0.3211 0 0);
	--sidebar-primary: oklch(0.777 0.1226 190.94);
	--sidebar-primary-foreground: oklch(1 0 0);
	--sidebar-accent: oklch(0.3351 0.0816 243.3375);
	--sidebar-accent-foreground: oklch(1 0 0);
	--sidebar-border: oklch(0.8975 0 0);
	--sidebar-ring: oklch(0.8537 0.0862 203.0686);
	--blackwhite: oklch(0 0 0);
	--gray: oklch(0.9649 0.002 275.36);
	--radius: 0.425rem;
	--shadow-2xs: 0px 2px 8px 0px hsl(0 0% 0% / 0.05);
	--shadow-xs: 0px 2px 8px 0px hsl(0 0% 0% / 0.05);
	--shadow-sm: 0px 2px 8px 0px hsl(0 0% 0% / 0.1), 0px 1px 2px -1px hsl(0 0% 0% / 0.1);
	--shadow: 0px 2px 8px 0px hsl(0 0% 0% / 0.1), 0px 1px 2px -1px hsl(0 0% 0% / 0.1);
	--shadow-md: 0px 2px 8px 0px hsl(0 0% 0% / 0.1), 0px 2px 4px -1px hsl(0 0% 0% / 0.1);
	--shadow-lg: 0px 2px 8px 0px hsl(0 0% 0% / 0.1), 0px 4px 6px -1px hsl(0 0% 0% / 0.1);
	--shadow-xl: 0px 2px 8px 0px hsl(0 0% 0% / 0.1), 0px 8px 10px -1px hsl(0 0% 0% / 0.1);
	--shadow-2xl: 0px 2px 8px 0px hsl(0 0% 0% / 0.25);
}

.dark {
	--background: oklch(0.2178 0 0);
	--foreground: oklch(1 0 0);
	--card: oklch(0.3052 0 0);
	--card-foreground: oklch(1 0 0);
	--popover: oklch(0.3052 0 0);
	--popover-foreground: oklch(1 0 0);
	--primary: oklch(0.777 0.1226 190.94);
	--primary-foreground: oklch(0.2178 0 0);
	--secondary: oklch(0.8537 0.0862 203.0686);
	--secondary-foreground: oklch(0.2178 0 0);
	--muted: oklch(0.5103 0 0);
	--muted-foreground: oklch(0.8452 0 0);
	--accent: oklch(0.3351 0.0816 243.3375);
	--accent-foreground: oklch(1 0 0);
	--destructive: oklch(0.652 0.234 26.6909);
	--destructive-foreground: oklch(0.2178 0 0);
	--border: oklch(0.3867 0 0);
	--input: oklch(0.3052 0 0);
	--ring: oklch(0.8537 0.0862 203.0686);
	--chart-1: oklch(0.777 0.1226 190.94);
	--chart-2: oklch(0.8537 0.0862 203.0686);
	--chart-3: oklch(0.3351 0.0816 243.3375);
	--chart-4: oklch(0.772 0.1738 64.552);
	--chart-5: oklch(0.652 0.234 26.6909);
	--sidebar: oklch(0.2178 0 0);
	--sidebar-foreground: oklch(1 0 0);
	--sidebar-primary: oklch(0.777 0.1226 190.94);
	--sidebar-primary-foreground: oklch(0.2178 0 0);
	--sidebar-accent: oklch(0.3351 0.0816 243.3375);
	--sidebar-accent-foreground: oklch(1 0 0);
	--sidebar-border: oklch(0.3867 0 0);
	--sidebar-ring: oklch(0.8537 0.0862 203.0686);
	--blackwhite: oklch(1 0 0);
	--gray: oklch(0.035 0 0);
	--radius: 0.425rem;
	--shadow-2xs: 0px 2px 8px 0px hsl(0 0% 0% / 0.1);
	--shadow-xs: 0px 2px 8px 0px hsl(0 0% 0% / 0.1);
	--shadow-sm: 0px 2px 8px 0px hsl(0 0% 0% / 0.2), 0px 1px 2px -1px hsl(0 0% 0% / 0.2);
	--shadow: 0px 2px 8px 0px hsl(0 0% 0% / 0.2), 0px 1px 2px -1px hsl(0 0% 0% / 0.2);
	--shadow-md: 0px 2px 8px 0px hsl(0 0% 0% / 0.2), 0px 2px 4px -1px hsl(0 0% 0% / 0.2);
	--shadow-lg: 0px 2px 8px 0px hsl(0 0% 0% / 0.2), 0px 4px 6px -1px hsl(0 0% 0% / 0.2);
	--shadow-xl: 0px 2px 8px 0px hsl(0 0% 0% / 0.2), 0px 8px 10px -1px hsl(0 0% 0% / 0.2);
	--shadow-2xl: 0px 2px 8px 0px hsl(0 0% 0% / 0.5);
}

@theme inline {
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-destructive: var(--destructive);
	--color-destructive-foreground: var(--destructive-foreground);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-ring: var(--ring);
	--color-chart-1: var(--chart-1);
	--color-chart-2: var(--chart-2);
	--color-chart-3: var(--chart-3);
	--color-chart-4: var(--chart-4);
	--color-chart-5: var(--chart-5);
	--color-sidebar: var(--sidebar);
	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-ring: var(--sidebar-ring);
	--color-blackwhite: var(--blackwhite);
	--color-gray: var(--gray);

	--radius-sm: calc(var(--radius) - 4px);
	--radius-md: calc(var(--radius) - 2px);
	--radius-lg: var(--radius);
	--radius-xl: calc(var(--radius) + 4px);

	--shadow-2xs: var(--shadow-2xs);
	--shadow-xs: var(--shadow-xs);
	--shadow-sm: var(--shadow-sm);
	--shadow: var(--shadow);
	--shadow-md: var(--shadow-md);
	--shadow-lg: var(--shadow-lg);
	--shadow-xl: var(--shadow-xl);
	--shadow-2xl: var(--shadow-2xl);
}

@layer base {
	* {
		@apply border-border outline-ring/50;
	}
	body {
		@apply bg-background text-foreground;
	}
}

/* Custom Classes */

/* Spacing Utilities */
@utility page-spacing {
	@apply flex w-full flex-col items-center justify-center;
}

@utility section-spacing {
	@apply mdlg:px-8 flex max-w-lg items-center justify-center px-3 md:px-5;
}

/* Text Utilities */
@utility headline-text {
	@apply text-headline/tight text-blackwhite md:text-headlinemd/tight mdlg:text-headlinemdlg/tight lg:text-headlinelg/tight font-[500] tracking-tight md:max-w-[50%];
}

@utility page-text {
	@apply text-page/normal text-muted-foreground md:text-pagemd font-[350];
}

@utility section-headline-text {
	@apply text-sectionheadline text-blackwhite md:text-sectionheadlinemd font-[500] tracking-tight;
}

@utility section-subheadline-text {
	@apply text-page/normal text-muted-foreground md:text-pagemd font-[400];
}

@utility subsection-subitem-text {
	@apply text-subsectionsubitem/normal text-muted-foreground md:text-subsectionsubitemmd font-[400] tracking-tight;
}

/* Accordion Animations */
@keyframes accordion-down {
	from {
		height: 0;
	}
	to {
		height: var(--radix-accordion-content-height);
	}
}

@keyframes accordion-up {
	from {
		height: var(--radix-accordion-content-height);
	}
	to {
		height: 0;
	}
}

.animate-accordion-down {
	animation: accordion-down 0.2s ease-out;
}

.animate-accordion-up {
	animation: accordion-up 0.2s ease-out;
}
