# Authentication Implementation

This document describes the complete authentication system implemented for the Evoprof SvelteKit application.

## Overview

The authentication system provides secure user login/logout functionality with JWT tokens, form validation using Zod schemas, and reactive state management using Svelte 5 runes.

## Architecture

### Frontend Components

1. **Authentication Store** (`src/lib/stores/auth.ts`)
   - Reactive authentication state using Svelte 5 runes
   - Token management and persistence
   - User session handling

2. **API Client** (`src/lib/api/client.ts`)
   - HTTP client for backend communication
   - Automatic token handling
   - Error handling and response formatting

3. **Form Validation** (`src/lib/schemas/auth.ts`)
   - Zod schemas for login form validation
   - Type-safe form data handling

4. **Login Page** (`src/routes/(public)/(auth)/login/+page.svelte`)
   - Complete login form with Superforms integration
   - Real-time validation and error display
   - Loading states and user feedback

### Backend Integration

- **Django REST Framework** with JWT authentication
- **Custom User Model** with roles and permissions
- **Token-based authentication** (access + refresh tokens)

## Features

### ✅ Implemented

- [x] **Zod Schema Validation**: Robust client-side form validation
- [x] **Superforms Integration**: Form state management and validation
- [x] **JWT Authentication**: Secure token-based authentication
- [x] **Reactive Auth Store**: Svelte 5 runes for state management
- [x] **Token Persistence**: localStorage with automatic initialization
- [x] **Error Handling**: Comprehensive error messages and user feedback
- [x] **Loading States**: Visual feedback during authentication
- [x] **Protected Routes**: Route guards for authenticated pages
- [x] **Automatic Redirects**: Post-login and logout redirects
- [x] **Environment Configuration**: Centralized config management
- [x] **Debug Logging**: Development-mode logging
- [x] **TypeScript Support**: Full type safety throughout

### 🚧 Planned Features

- [ ] **Google OAuth Integration**: Social login support
- [ ] **Token Auto-refresh**: Automatic token renewal
- [ ] **Remember Me**: Extended session persistence
- [ ] **Password Reset**: Complete password reset flow
- [ ] **Registration**: User registration functionality

## Usage

### Basic Login Flow

1. User navigates to `/login`
2. Enters email and password
3. Form validates using Zod schema
4. Superforms handles form submission
5. API client communicates with Django backend
6. Tokens stored in localStorage and auth store
7. User redirected to dashboard or intended page

### Authentication Check

```typescript
import { authStore } from '$lib/stores/auth';

// Check if user is authenticated
if (authStore.state.isAuthenticated) {
  // User is logged in
  console.log('User:', authStore.state.user);
}

// Login programmatically
const success = await authStore.login({
  email: '<EMAIL>',
  password: 'password123'
});

// Logout
authStore.logout();
```

### Protected Routes

Routes under `(protected)` group automatically check authentication:

```
src/routes/
├── (public)/
│   ├── (auth)/
│   │   └── login/
│   └── +page.svelte
└── (protected)/
    ├── +layout.svelte  # Auth guard
    └── dashboard/
        └── +page.svelte
```

## Configuration

### Environment Variables

Create `.env` file in the client directory:

```env
PUBLIC_API_BASE_URL=http://localhost:8000
PUBLIC_ENABLE_GOOGLE_AUTH=false
PUBLIC_MOCK_API=false
NODE_ENV=development
```

### API Configuration

Update `src/lib/config/env.ts` for different environments:

```typescript
export const API_CONFIG = {
  BASE_URL: env.PUBLIC_API_BASE_URL || 'http://localhost:8000',
  ENDPOINTS: {
    LOGIN: '/auth/login/',
    REFRESH: '/auth/token/refresh/',
    // ...
  }
};
```

## File Structure

```
src/
├── lib/
│   ├── api/
│   │   └── client.ts           # API client
│   ├── config/
│   │   └── env.ts              # Environment config
│   ├── schemas/
│   │   └── auth.ts             # Zod validation schemas
│   └── stores/
│       └── auth.ts             # Authentication store
├── routes/
│   ├── (protected)/
│   │   ├── +layout.svelte      # Auth guard layout
│   │   └── dashboard/
│   │       └── +page.svelte    # Protected dashboard
│   ├── (public)/
│   │   └── (auth)/
│   │       └── login/
│   │           ├── +page.svelte        # Login form
│   │           └── +page.server.ts     # Server action
│   └── api/
│       └── auth/
│           ├── login/
│           │   └── +server.ts  # Login API route
│           └── logout/
│               └── +server.ts  # Logout API route
└── types/
    └── auth.ts                 # TypeScript interfaces
```

## Security Considerations

1. **Token Storage**: Tokens stored in localStorage (consider httpOnly cookies for production)
2. **HTTPS**: Always use HTTPS in production
3. **Token Expiration**: Implement automatic token refresh
4. **CSRF Protection**: Django CSRF middleware enabled
5. **Input Validation**: Client and server-side validation
6. **Error Handling**: No sensitive information in error messages

## Testing

To test the authentication system:

1. Start the Django backend server
2. Start the SvelteKit development server
3. Navigate to `/login`
4. Use valid credentials from your Django user database
5. Verify redirect to dashboard
6. Test logout functionality

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure Django CORS settings allow frontend domain
2. **Token Errors**: Check Django JWT settings and token expiration
3. **Validation Errors**: Verify Zod schema matches backend requirements
4. **Redirect Issues**: Check route configuration and auth guards

### Debug Mode

Enable debug logging by setting `NODE_ENV=development`:

```typescript
import { debugLog } from '$lib/config/env';
debugLog('Authentication event', data);
```

## Contributing

When extending the authentication system:

1. Update TypeScript interfaces in `src/types/auth.ts`
2. Add new validation schemas to `src/lib/schemas/auth.ts`
3. Extend the auth store with new methods
4. Update this documentation

## Dependencies

- **zod**: Schema validation
- **sveltekit-superforms**: Form handling
- **formsnap**: Form components
- **svelte-sonner**: Toast notifications

All dependencies are already included in the project.
