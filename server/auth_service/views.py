from rest_framework import generics
from django.contrib.auth import get_user_model
from .permissions import HasPermission


from .models import UserRole, Permission
from .serializers import (
    UserSerializer,
    UserCreateSerializer,
    UserRoleSerializer,
    PermissionSerializer,
    MyTokenObtainPairSerializer,
)
from rest_framework_simplejwt.views import TokenObtainPairView

# Get the active User model from settings.AUTH_USER_MODEL for compatibility with custom user models
CustomUser = get_user_model()

class MyTokenObtainPairView(TokenObtainPairView):
    serializer_class = MyTokenObtainPairSerializer


# === User Views ===

class UserListCreateAPIView(generics.ListCreateAPIView):
    """
    Handles listing all users and creating a new user.
    Dynamically switches serializer based on HTTP method.
    """
    queryset = CustomUser.objects.all()

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return UserCreateSerializer
        return UserSerializer


class UserRetrieveUpdateDestroyAPIView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a user by ID.
    Enforces permission 'edit_user' via HasPermission class.
    """
    queryset = CustomUser.objects.all()
    serializer_class = UserSerializer
    # permission_classes = [HasPermission]
    # required_permission = 'edit_user'



# === UserRole Views ===

class UserRoleListCreateAPIView(generics.ListCreateAPIView):
    """
    List all user roles or create a new role.
    """
    queryset = UserRole.objects.all()
    serializer_class = UserRoleSerializer


class UserRoleRetrieveUpdateDestroyAPIView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a user role by ID.
    """
    queryset = UserRole.objects.all()
    serializer_class = UserRoleSerializer


# === Permission Views ===

class PermissionListCreateAPIView(generics.ListCreateAPIView):
    """
    List all permissions or create a new permission.
    """
    queryset = Permission.objects.all()
    serializer_class = PermissionSerializer


class PermissionRetrieveUpdateDestroyAPIView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a permission by ID.
    """
    queryset = Permission.objects.all()
    serializer_class = PermissionSerializer
