"""
Serializers for User, UserRole, and Permission models.

Includes:
- JWT token customization (MyTokenObtainPairSerializer)
- Nested and writable representations of related models
- Separate serializers for user creation and update
"""

from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from django.contrib.auth import get_user_model
from .models import UserRole, Permission

# Use get_user_model() to get the active User model as defined in settings.AUTH_USER_MODEL.
# This ensures compatibility if the User model is customized or swapped in the project settings.
CustomUser = get_user_model()

class MyTokenObtainPairSerializer(TokenObtainPairSerializer):
    """
    Customize JWT token to include username and email as claims.
    Uses email for authentication instead of username.
    """
    # Override the username field to use email
    username_field = 'email'
    
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)
        token['username'] = user.username
        token['email'] = user.email
        return token

    def validate(self, attrs):
        # The parent class will now use email field for authentication
        # since we set username_field = 'email'
        return super().validate(attrs)


class PermissionSerializer(serializers.ModelSerializer):
    """
    Serializer for Permission model.

    - user_role: read-only string representation
    - user_role_id: write-only FK for creating/updating
    """
    user_role = serializers.StringRelatedField(read_only=True)
    user_role_id = serializers.PrimaryKeyRelatedField(
        queryset=UserRole.objects.all(),
        source='user_role',
        write_only=True
    )

    class Meta:
        model = Permission
        fields = [
            'permission_id',
            'permission_name',
            'permission_description',
            'user_role',
            'user_role_id',
            'created_at',
            'updated_at'
        ]


class UserRoleSerializer(serializers.ModelSerializer):
    """
    Serializer for UserRole model with nested permissions.
    """
    permissions = PermissionSerializer(many=True, read_only=True)

    class Meta:
        model = UserRole
        fields = '__all__'


class UserRoleMiniSerializer(serializers.ModelSerializer):
    """
    Minimal serializer for UserRole used in nested UserSerializer.

    Purpose:
    - Provide only essential fields to avoid overfetching data.
    - Used for embedding user role info inside User responses.

    Fields:
    - user_role_id: UUID primary key of the role.
    - role_name: Human-readable name of the role.
    """

    class Meta:
        model = UserRole
        fields = ['user_role_id', 'role_name']


class UserSerializer(serializers.ModelSerializer):
    """
    Serializer for User model.

    - user_role: nested minimal role info (read-only)
    - user_role_id: write-only FK for assigning role
    """
    user_role = UserRoleMiniSerializer(read_only=True)
    user_role_id = serializers.PrimaryKeyRelatedField(
        queryset=UserRole.objects.all(),
        source='user_role',
        write_only=True,
        allow_null=True
    )

    class Meta:
        model = CustomUser
        exclude = ['password']


class UserCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating new users.

    - Accepts password as write-only field
    - Handles password hashing on create
    - Allows assigning user_role by ID
    """
    password = serializers.CharField(write_only=True)
    user_role_id = serializers.PrimaryKeyRelatedField(
        queryset=UserRole.objects.all(),
        source='user_role',
        write_only=True,
        allow_null=True
    )

    class Meta:
        model = CustomUser
        fields = [
            'id',
            'username',
            'email',
            'password',
            'user_role_id'
        ]

    def create(self, validated_data):
        password = validated_data.pop('password')
        user = CustomUser(**validated_data)
        user.set_password(password)
        user.save()
        return user
