from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView

from .views import (
    UserListCreateAPIView,
    UserRetrieveUpdateDestroyAPIView,
    UserRoleListCreateAPIView,
    UserRoleRetrieveUpdateDestroyAPIView,
    PermissionListCreateAPIView,
    PermissionRetrieveUpdateDestroyAPIView,
    MyTokenObtainPairView
)

urlpatterns = [
    # User URLs
    path('users/', UserListCreateAPIView.as_view(), name='user-list-create'),
    path('users/<uuid:pk>/', UserRetrieveUpdateDestroyAPIView.as_view(), name='user-detail'),

    # UserRole URLs
    path('user-roles/', UserRoleListCreateAPIView.as_view(), name='userrole-list-create'),
    path('user-roles/<int:pk>/', UserRoleRetrieveUpdateDestroyAPIView.as_view(), name='userrole-detail'),

    # Permission URLs
    path('permissions/', PermissionListCreateAPIView.as_view(), name='permission-list-create'),
    path('permissions/<int:pk>/', PermissionRetrieveUpdateDestroyAPIView.as_view(), name='permission-detail'),

    # Access and Refresh Tokens
    path('login/', MyTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
]
