from rest_framework.permissions import BasePermission

class HasPermission(BasePermission):
    """
    Custom permission to check if the authenticated user
    has the required permission defined by the view's
    `required_permission` attribute.

    - If no `required_permission` is set on the view, access is allowed.
    - Superusers bypass all permission checks.
    - Permissions are checked via the user's related UserRole and its permissions.
    """

    def has_permission(self, request, view):
        permission_name = getattr(view, 'required_permission', None)
        if permission_name is None:
            # No specific permission required, allow access
            return True

        user = request.user
        if not user or not user.is_authenticated:
            return False

        # Superusers have unrestricted access
        if getattr(user, 'is_superuser', False):
            return True

        user_role = getattr(user, 'user_role', None)
        if not user_role:
            return False

        # Check if the user's role includes the required permission
        return user_role.permissions.filter(permission_name=permission_name).exists()
