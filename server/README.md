# Django Backend Service

This is the backend server for the **Refactored Memory** project, built with Django and Django REST Framework.

---

## 🚀 Getting Started

### 1. Clone the repository

```bash
git clone https://github.com/your-username/refactored-memory.git
cd refactored-memory/server
```

### 2. Create and activate a virtual environment

```bash
virtualenv env
# Windows
env\Scripts\activate
# macOS/Linux
source env/bin/activate
```

### 3. Install dependencies

```bash
pip install -r requirements.txt
```

If you don't have requirements.txt yet, generate it after installing packages:

```bash
pip freeze > requirements.txt
```

### 4. Run initial setup

```bash
python manage.py makemigrations
python manage.py migrate
```

### 5. Start the development server

```bash
python manage.py runserver
```

---

## 📚 API Endpoints

### Authentication Endpoints

| Method | Endpoint               | Description                          |
| ------ | ---------------------- | ------------------------------------ |
| `POST` | `/auth/login/`         | Obtain JWT access and refresh tokens |
| `POST` | `/auth/token/refresh/` | Refresh JWT access token             |

### User Management

| Method   | Endpoint              | Description             |
| -------- | --------------------- | ----------------------- |
| `GET`    | `/auth/users/`        | List all users          |
| `POST`   | `/auth/users/`        | Create a new user       |
| `GET`    | `/auth/users/{uuid}/` | Retrieve user details   |
| `PUT`    | `/auth/users/{uuid}/` | Update a user           |
| `PATCH`  | `/auth/users/{uuid}/` | Partially update a user |
| `DELETE` | `/auth/users/{uuid}/` | Delete a user           |

### Role Management

| Method   | Endpoint                 | Description             |
| -------- | ------------------------ | ----------------------- |
| `GET`    | `/auth/user-roles/`      | List all user roles     |
| `POST`   | `/auth/user-roles/`      | Create a new user role  |
| `GET`    | `/auth/user-roles/{id}/` | Retrieve role details   |
| `PUT`    | `/auth/user-roles/{id}/` | Update a role           |
| `PATCH`  | `/auth/user-roles/{id}/` | Partially update a role |
| `DELETE` | `/auth/user-roles/{id}/` | Delete a role           |

### Permission Management

| Method   | Endpoint                  | Description                   |
| -------- | ------------------------- | ----------------------------- |
| `GET`    | `/auth/permissions/`      | List all permissions          |
| `POST`   | `/auth/permissions/`      | Create a new permission       |
| `GET`    | `/auth/permissions/{id}/` | Retrieve permission details   |
| `PUT`    | `/auth/permissions/{id}/` | Update a permission           |
| `PATCH`  | `/auth/permissions/{id}/` | Partially update a permission |
| `DELETE` | `/auth/permissions/{id}/` | Delete a permission           |
